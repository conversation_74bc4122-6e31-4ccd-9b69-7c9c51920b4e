# account/admin.py
import openpyxl
from django.contrib import admin, messages
from django import forms
from django.urls import path
from django.shortcuts import render, redirect
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from .models import Employee
from orgunit_app.models import Department, Location, Position
from mptt.forms import TreeNodeChoiceField

admin.site.site_header = "Employee Attendance & Leave Mgt. - Admin"
admin.site.site_title = "Employee MGT Portal"
admin.site.index_title = "Welcome to Attendance & Leave Management System"

# --- Custom Form for Excel Upload (general employee upload) ---
class ExcelUploadForm(forms.Form):
    excel_file = forms.FileField(
        label="Upload Excel File",
        help_text="Upload an Excel file (.xlsx) containing employee data (emp_id, Fname, Lname, Gender).",
        widget=forms.FileInput(attrs={'accept': '.xlsx'})
    )

# --- Custom ModelForm for Employee Admin to use Tree Dropdown (for add/change forms) ---
class EmployeeAdminForm(forms.ModelForm):
    department = TreeNodeChoiceField(
        queryset=Department.objects.all(),
        label="Department",
        required=False,
        empty_label="--- Select Department ---",
    )

    class Meta:
        model = Employee
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['department'].label_from_instance = lambda obj: obj.get_tree_display_name()


# --- Custom SimpleListFilter for Department Tree View ---
class DepartmentTreeFilter(admin.SimpleListFilter):
    title = _('Department')
    parameter_name = 'department'

    def lookups(self, request, model_admin):
        departments = Department.objects.all().order_by('tree_id', 'lft')
        lookups = []
        for dept in departments:
            lookups.append((str(dept.pk), dept.get_tree_display_name()))
        return lookups

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(department__id=self.value())
        return queryset

# --- Custom SimpleListFilter for Location ---
class LocationFilter(admin.SimpleListFilter):
    title = _("Location")  # Display name in the admin sidebar
    parameter_name = "location"  # URL query parameter

    def lookups(self, request, model_admin):
        """Defines filter options based on existing locations."""
        locations = Location.objects.all()
        return [(loc.id, loc.name) for loc in locations]

    def queryset(self, request, queryset):
        """Filters employees based on selected location."""
        if self.value():
            return queryset.filter(department__location_id=self.value())
        
# --- Employee Admin Configuration ---
class EmployeeAdmin(admin.ModelAdmin):
    form = EmployeeAdminForm

    list_display = ('emp_id', 'Fname', 'Lname', 'Gender', 'user_username', 'department_plain_name_display', 'position', 'current_location_display')
    search_fields = ('emp_id', 'Fname', 'Lname', 'department__dept_name', 'position__title')
    list_filter = ('Gender', LocationFilter, 'position') # DepartmentTreeFilter

    exclude = ('user',)

    def department_plain_name_display(self, obj):
        return obj.department.dept_name if obj.department else '-'
    department_plain_name_display.short_description = 'Department'
    department_plain_name_display.admin_order_field = 'department__dept_name'

    def current_location_display(self, obj):
        return obj.current_location.name if obj.current_location else '-'
    current_location_display.short_description = 'Current Location'
    current_location_display.admin_order_field = 'department__location__name'

    def user_username(self, obj):
        return obj.user.username if obj.user else '-'
    user_username.short_description = 'User Account'

    change_list_template = "admin/employee_changelist.html"

    def get_urls(self):
        urls = super().get_urls()
        my_urls = [
            path('import-excel/', self.admin_site.admin_view(self.import_excel), name='employee_import_excel'),
        ]
        return my_urls + urls

    def import_excel(self, request):
        """
        Handles the Excel file upload and processes the data for bulk employee creation.
        """
        if request.method == "POST":
            form = ExcelUploadForm(request.POST, request.FILES)
            if form.is_valid():
                excel_file = form.cleaned_data['excel_file']

                if not excel_file.name.endswith('.xlsx'):
                    self.message_user(request, "Invalid file format. Please upload an .xlsx file.", level=messages.ERROR)
                    return redirect("..")

                try:
                    workbook = openpyxl.load_workbook(excel_file)
                    sheet = workbook.active

                    header = [cell.value for cell in sheet[1]]
                    expected_headers = ['emp_id', 'Fname', 'Lname', 'Gender']

                    if not all(h in header for h in expected_headers):
                        self.message_user(request, "Excel file must contain 'emp_id', 'Fname', 'Lname', 'Gender' columns.", level=messages.ERROR)
                        return redirect("..")

                    created_count = 0
                    updated_count = 0
                    skipped_count = 0
                    errors = []

                    for row_index, row in enumerate(sheet.iter_rows(min_row=2, values_only=True), start=2):
                        row_data = dict(zip(header, row))

                        emp_id = str(row_data.get('emp_id', '')).strip()
                        Fname = str(row_data.get('Fname', '')).strip()
                        Lname = str(row_data.get('Lname', '')).strip()
                        Gender = str(row_data.get('Gender', '')).strip().upper()

                        if not (emp_id and Fname and Lname and Gender):
                            errors.append(f"Row {row_index}: Skipping due to missing required data (emp_id, Fname, Lname, Gender are all required).")
                            skipped_count += 1
                            continue

                        if Gender not in ['M', 'F', 'O']:
                            errors.append(f"Row {row_index}: Invalid Gender '{Gender}'. Must be M, F, or O.")
                            skipped_count += 1
                            continue

                        try:
                            employee, created = Employee.objects.get_or_create(
                                emp_id=emp_id,
                                defaults={
                                    'Fname': Fname,
                                    'Lname': Lname,
                                    'Gender': Gender,
                                }
                            )

                            if created:
                                created_count += 1
                            else:
                                if (employee.Fname != Fname or
                                    employee.Lname != Lname or
                                    employee.Gender != Gender):
                                    employee.Fname = Fname
                                    employee.Lname = Lname
                                    employee.Gender = Gender
                                    employee.save()
                                updated_count += 1

                        except Exception as e:
                            errors.append(f"Row {row_index} (emp_id: {emp_id}): Error processing employee: {e}")
                            skipped_count += 1

                    self.message_user(request, f"Bulk import complete: {created_count} employees created, {updated_count} updated, {skipped_count} skipped.", level=messages.SUCCESS)
                    for error_msg in errors:
                        self.message_user(request, error_msg, level=messages.WARNING)

                    return redirect("..")

                except Exception as e:
                    self.message_user(request, f"Error processing Excel file: {e}", level=messages.ERROR)
                    return redirect("..")
            else:
                self.message_user(request, "Form validation failed. Please check your file.", level=messages.ERROR)
                return render(request, self.change_list_template, {'form': form, **self.admin_site.each_context(request)})

        form = ExcelUploadForm()
        return render(request, 'admin/import_excel_form.html', {'form': form, 'title': 'Import Employees', **self.admin_site.each_context(request)})

    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        extra_context['excel_upload_form'] = ExcelUploadForm()
        return super().changelist_view(request, extra_context=extra_context)

admin.site.register(Employee, EmployeeAdmin)


# --- Inline for Employees to be used in DepartmentAdmin ---
class EmployeeInline(admin.TabularInline):
    model = Employee
    # Specify the fields to display in the inline form
    fields = ('emp_id', 'Fname', 'Lname', 'Gender', 'position')
    readonly_fields = ('emp_id', 'Fname', 'Lname', 'Gender') # Make core employee fields read-only
    extra = 0 # Don't show extra empty forms
    can_delete = False # Prevent deleting employees directly from this inline
    show_change_link = True # Allow clicking to the full Employee change page
    verbose_name = "Employee"
    verbose_name_plural = "Employees in this Department"

    # You might want to prevent adding new employees via this inline
    # Instead, use the bulk upload or the main Employee admin page
    def has_add_permission(self, request, obj=None):
        return False
