import time
from django.apps import AppConfig


class AccountsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'accounts'
    verbose_name = "Employee system account"

    def ready(self):
        # Import your signals here to ensure they are registered
        import accounts.signals
        print(f"accounts_app signals loaded. {time.strftime('%Y-%m-%d %H:%M:%S')}") # For debugging/confirmation