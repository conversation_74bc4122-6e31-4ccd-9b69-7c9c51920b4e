from django.db import models
from django.contrib.auth.models import User
from orgunit_app.models import Location, Department, Position # Import models from org_unit

class Employee(models.Model):
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    ]

    emp_id = models.CharField(max_length=20, unique=True, help_text="Employee ID (will be used as username)")
    Fname = models.CharField(max_length=100, verbose_name="First Name")
    Lname = models.CharField(max_length=100, verbose_name="Last Name")
    Gender = models.CharField(max_length=1, choices=GENDER_CHOICES)

    user = models.OneToOneField(User, on_delete=models.CASCADE, null=True, blank=True, related_name='employee_profile')

    # current_location, current_department, current_position fields are removed.
    # They will now be dynamic properties derived from history models below.

    class Meta:
        verbose_name = "Employee"
        verbose_name_plural = "Employees"
        ordering = ['Lname', 'Fname']

    def __str__(self):
        return f"{self.Fname} {self.Lname} ({self.emp_id})"

    @property
    def current_department(self):
        """
        Returns the employee's current department from their history.
        The "current" department is the one with the latest start_date and no end_date.
        """
        try:
            return self.department_history.filter(end_date__isnull=True).latest('start_date').department
        except EmployeeDepartmentHistory.DoesNotExist:
            return None

    @property
    def current_position(self):
        """
        Returns the employee's current position from their history.
        The "current" position is the one with the latest start_date and no end_date.
        """
        try:
            return self.position_history.filter(end_date__isnull=True).latest('start_date').position
        except EmployeePositionHistory.DoesNotExist:
            return None

    @property
    def current_location(self):
        """
        Returns the employee's current location, derived from their current department.
        """
        current_dept = self.current_department
        if current_dept and current_dept.location:
            return current_dept.location
        return None

# --- History Models ---

class EmployeeDepartmentHistory(models.Model):
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='department_history')
    department = models.ForeignKey(Department, on_delete=models.CASCADE)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Employee Department History"
        verbose_name_plural = "Employee Department Histories"
        ordering = ['employee', '-start_date']
        get_latest_by = 'start_date'

    def __str__(self):
        end_display = self.end_date if self.end_date else 'Present'
        return f"{self.employee.emp_id} - {self.department.dept_name} ({self.start_date} to {end_display})"

class EmployeePositionHistory(models.Model):
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='position_history')
    position = models.ForeignKey(Position, on_delete=models.CASCADE)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Employee Position History"
        verbose_name_plural = "Employee Position Histories"
        ordering = ['employee', '-start_date']
        get_latest_by = 'start_date'

    def __str__(self):
        end_display = self.end_date if self.end_date else 'Present'
        return f"{self.employee.emp_id} - {self.position.title} ({self.start_date} to {end_display})"

# EmployeeLocationHistory model has been removed.
