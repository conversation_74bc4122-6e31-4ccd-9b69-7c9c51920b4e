# Generated by Django 5.2.3 on 2025-06-17 08:52

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('emp_id', models.Char<PERSON>ield(help_text='Employee ID (will be used as username)', max_length=20, unique=True)),
                ('Fname', models.CharField(max_length=100, verbose_name='First Name')),
                ('Lname', models.CharField(max_length=100, verbose_name='Last Name')),
                ('Gender', models.Char<PERSON>ield(choices=[('M', 'Male'), ('F', 'Female')], max_length=1)),
                ('user', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='employee_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Employee',
                'verbose_name_plural': 'Employees',
                'ordering': ['Lname', 'Fname'],
            },
        ),
    ]
