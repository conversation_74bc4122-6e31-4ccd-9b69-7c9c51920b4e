# Generated by Django 5.2.3 on 2025-06-17 12:33

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
        ('orgunit_app', '0002_position'),
    ]

    operations = [
        migrations.AlterField(
            model_name='employee',
            name='Gender',
            field=models.CharField(choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], max_length=1),
        ),
        migrations.CreateModel(
            name='EmployeeDepartmentHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTime<PERSON>ield(auto_now=True)),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='orgunit_app.department')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='department_history', to='accounts.employee')),
            ],
            options={
                'verbose_name': 'Employee Department History',
                'verbose_name_plural': 'Employee Department Histories',
                'ordering': ['employee', '-start_date'],
                'get_latest_by': 'start_date',
            },
        ),
        migrations.CreateModel(
            name='EmployeePositionHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='position_history', to='accounts.employee')),
                ('position', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='orgunit_app.position')),
            ],
            options={
                'verbose_name': 'Employee Position History',
                'verbose_name_plural': 'Employee Position Histories',
                'ordering': ['employee', '-start_date'],
                'get_latest_by': 'start_date',
            },
        ),
    ]
