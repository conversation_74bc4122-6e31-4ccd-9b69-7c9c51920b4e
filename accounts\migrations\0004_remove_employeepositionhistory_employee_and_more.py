# Generated by Django 5.2.3 on 2025-06-17 13:53

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_alter_employee_gender'),
        ('orgunit_app', '0003_position_level'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='employeepositionhistory',
            name='employee',
        ),
        migrations.RemoveField(
            model_name='employeepositionhistory',
            name='position',
        ),
        migrations.AddField(
            model_name='employee',
            name='department',
            field=models.ForeignKey(blank=True, help_text="The employee's current assigned department", null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employees_in_department', to='orgunit_app.department'),
        ),
        migrations.AddField(
            model_name='employee',
            name='position',
            field=models.ForeignKey(blank=True, help_text="The employee's current job position", null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employees_in_position', to='orgunit_app.position'),
        ),
        migrations.DeleteModel(
            name='EmployeeDepartmentHistory',
        ),
        migrations.DeleteModel(
            name='EmployeePositionHistory',
        ),
    ]
