# myapp/models.py
from django.db import models
from django.contrib.auth.models import User
from orgunit_app.models import Department, Position # Import Department and Position from orgunit_app

class Employee(models.Model):
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
    ]

    emp_id = models.CharField(max_length=20, unique=True, help_text="Employee ID (will be used as username)")
    Fname = models.Char<PERSON>ield(max_length=100, verbose_name="First Name")
    Lname = models.Char<PERSON>ield(max_length=100, verbose_name="Last Name")
    Gender = models.CharField(max_length=1, choices=GENDER_CHOICES)

    user = models.OneToOneField(User, on_delete=models.CASCADE, null=True, blank=True, related_name='employee_profile')

    # Directly add Department and Position fields to Employee model
    department = models.ForeignKey(
        Department,
        on_delete=models.SET_NULL, # If department is deleted, employee's department becomes null
        null=True,
        blank=True,
        related_name='employees_in_department',
        help_text="The employee's current assigned department"
    )
    position = models.ForeignKey(
        Position,
        on_delete=models.SET_NULL, # If position is deleted, employee's position becomes null
        null=True,
        blank=True,
        related_name='employees_in_position',
        help_text="The employee's current job position"
    )

    class Meta:
        verbose_name = "Employee"
        verbose_name_plural = "Employees"
        ordering = ['Lname', 'Fname']

    def __str__(self):
        return f"{self.Fname} {self.Lname} ({self.emp_id})"

    @property
    def current_location(self):
        """
        Returns the employee's current location, derived from their assigned department.
        """
        if self.department and self.department.location:
            return self.department.location
        return None

# --- History Models Removed ---
# EmployeeDepartmentHistory and EmployeePositionHistory models are removed as per new requirements.
