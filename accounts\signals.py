# myapp/signals.py
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth.models import User
from .models import Employee

@receiver(post_save, sender=Employee)
def create_or_update_user_on_employee_save(sender, instance, created, **kwargs):
    """
    Signal receiver to create or update a User object
    whenever an Employee object is created or saved.
    """
    if created:
        # If a new Employee is created, create a new User.
        try:
            # Check if a user with this emp_id already exists to prevent duplicates
            user, user_created = User.objects.get_or_create(
                username=instance.emp_id,
                defaults={
                    'first_name': instance.Fname,
                    'last_name': instance.Lname,
                    'is_staff': False, # Employees might not be staff
                    'is_active': True,
                }
            )
            if user_created:
                # Set a default password. It's recommended to make this more secure.
                # For a real application, you might want a more complex default
                # or force a password reset on first login.
                user.set_password(instance.emp_id) # Using emp_id as default password
                user.save()
                print(f"User '{user.username}' created successfully for new employee.")
            else:
                print(f"User '{user.username}' already exists for this employee ID.")

            # Link the newly created or existing user to the employee
            instance.user = user
            instance.save(update_fields=['user']) # Save the employee instance to link the user
            print(f"Employee '{instance.emp_id}' linked to User '{user.username}'.")

        except Exception as e:
            # Log any errors that occur during user creation
            print(f"Error creating/linking user for employee {instance.emp_id}: {e}")
    else:
        # If the Employee is updated, update the associated User's details (optional)
        if instance.user:
            # Only update if the employee's name changes, for example
            if instance.user.first_name != instance.Fname or instance.user.last_name != instance.Lname:
                instance.user.first_name = instance.Fname
                instance.user.last_name = instance.Lname
                instance.user.save()
                print(f"User '{instance.user.username}' updated for employee '{instance.emp_id}'.")

