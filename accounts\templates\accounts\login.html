<!DOCTYPE html>
<html lang="en">
<head>
    {% load static %}
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - EEU Employee Leave & Attendance</title>
    <link rel="stylesheet" href="{% static 'dist/tailwind.css' %}">
    <link rel="stylesheet" href="{% static 'fontawesome/css/all.min.css' %}">
</head>
<body class="bg-gray-100">
    <div class="flex min-h-screen items-center justify-center p-4">
        <div class="w-full max-w-md border-t-2 border-emerald-500 overflow-hidden rounded-lg bg-white shadow-xl">
            <!-- Logo Header -->
            <div class=" p-6 text-center">
                <img src="{% static 'imgs/eeu_logo.png' %}" alt="EEU Logo" class="mx-auto mb-3 h-20">
                <h2 class="text-xl font-bold text-emerald-600 mb-2">EEU Attendnace and Leave System</h2>
                <p class="text-gray-500 mb-3 text-center">Sign in to your account</p>
                <!-- <h2 class="mt-4 text-2xl font-bold text-gray-800">Employee Leave & Attendance</h2> -->
            </div>
            
            <!-- Login Form -->
            <div class="p-8">
                <!-- <h1 class="mb-6 text-xl font-bold text-gray-800">Sign In</h1> -->

                <form method="post">
                    {% csrf_token %}

                    <div class="mb-4">
                        <label for="{{ form.username.id_for_label }}" class="mb-2 block text-sm font-medium text-gray-700">
                            Username
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <i class="fas fa-user text-emerald-500"></i>
                            </div>
                            <input type="text" name="{{ form.username.html_name }}" id="{{ form.username.id_for_label }}"
                                   class="w-full rounded-md border border-gray-300 pl-10 pr-3 py-2 focus:border-emerald-500 focus:outline-none focus:ring-1 focus:ring-emerald-500 transition-colors"
                                   placeholder="Enter your username"
                                   {% if form.username.value %}value="{{ form.username.value }}"{% endif %} required>
                        </div>
                        {% if form.username.errors %}
                            <p class="mt-1 text-sm text-red-600">
                                <i class="fas fa-exclamation-circle mr-1"></i>{{ form.username.errors.0 }}
                            </p>
                        {% endif %}
                    </div>

                    <div class="mb-6">
                        <label for="{{ form.password.id_for_label }}" class="mb-2 block text-sm font-medium text-gray-700">
                            Password
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <i class="fas fa-lock text-emerald-500"></i>
                            </div>
                            <input type="password" name="{{ form.password.html_name }}" id="{{ form.password.id_for_label }}"
                                   class="w-full rounded-md border border-gray-300 pl-10 pr-3 py-2 focus:border-emerald-500 focus:outline-none focus:ring-1 focus:ring-emerald-500 transition-colors"
                                   placeholder="Enter your password" required>
                        </div>
                        {% if form.password.errors %}
                            <p class="mt-1 text-sm text-red-600">
                                <i class="fas fa-exclamation-circle mr-1"></i>{{ form.password.errors.0 }}
                            </p>
                        {% endif %}
                    </div>

                    {% if form.non_field_errors %}
                        <div class="mb-4 rounded-md bg-red-50 border border-red-200 p-3">
                            {% for error in form.non_field_errors %}
                                <p class="text-sm text-red-600">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>{{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- <button type="submit" class="w-full rounded-md bg-emerald-500 py-3 px-4 font-medium text-white transition-all duration-200 hover:bg-emerald-600 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 shadow-md hover:shadow-lg">
                        <i class="fas fa-sign-in-alt mr-2"></i>Sign In
                    </button> -->

                    
                    <button type="submit" class="mt-4 w-full py-2 rounded-lg bg-emerald-500 hover:bg-emerald-600 text-white font-semibold shadow transition cursor-pointer">
                        <i class="fas fa-sign-in-alt mr-2"></i>Sign In
                    </button>
                </form>
                <div class="mt-6 text-center text-sm text-gray-500">
                    &copy; <span id="year"></span> Ethiopian Electric Utility
                </div>
            </div> <!-- # end of form-akafi -->
            
        </div>
    </div>
</body>
</html>
