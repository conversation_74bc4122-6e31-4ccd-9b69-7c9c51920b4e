{% extends 'base.html' %}

{% block title %}Change Password - EEU System{% endblock %}

{% block page_title %}Change Password{% endblock %}
{% block page_subtitle %}Update your account password{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-2">
                <i class="fas fa-key text-emerald-500 mr-2"></i>
                Change Your Password
            </h2>
            <p class="text-gray-600">
                Please enter your current password and choose a new password.
            </p>
        </div>

        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div>
                <label for="{{ form.old_password.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Current Password
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <i class="fas fa-lock text-emerald-500"></i>
                    </div>
                    <input type="password" name="{{ form.old_password.html_name }}" id="{{ form.old_password.id_for_label }}"
                           class="w-full rounded-md border border-gray-300 pl-10 pr-3 py-2 focus:border-emerald-500 focus:outline-none focus:ring-1 focus:ring-emerald-500 transition-colors"
                           placeholder="Enter current password" required>
                </div>
                {% if form.old_password.errors %}
                    <div class="text-red-600 text-sm mt-1">
                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.old_password.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.new_password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    New Password
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <i class="fas fa-key text-emerald-500"></i>
                    </div>
                    <input type="password" name="{{ form.new_password1.html_name }}" id="{{ form.new_password1.id_for_label }}"
                           class="w-full rounded-md border border-gray-300 pl-10 pr-3 py-2 focus:border-emerald-500 focus:outline-none focus:ring-1 focus:ring-emerald-500 transition-colors"
                           placeholder="Enter new password" required>
                </div>
                {% if form.new_password1.errors %}
                    <div class="text-red-600 text-sm mt-1">
                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.new_password1.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.new_password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Confirm New Password
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <i class="fas fa-key text-emerald-500"></i>
                    </div>
                    <input type="password" name="{{ form.new_password2.html_name }}" id="{{ form.new_password2.id_for_label }}"
                           class="w-full rounded-md border border-gray-300 pl-10 pr-3 py-2 focus:border-emerald-500 focus:outline-none focus:ring-1 focus:ring-emerald-500 transition-colors"
                           placeholder="Confirm new password" required>
                </div>
                {% if form.new_password2.errors %}
                    <div class="text-red-600 text-sm mt-1">
                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.new_password2.errors.0 }}
                    </div>
                {% endif %}
            </div>

            {% if form.non_field_errors %}
                <div class="rounded-md bg-red-50 border border-red-200 p-3">
                    {% for error in form.non_field_errors %}
                        <p class="text-sm text-red-600">
                            <i class="fas fa-exclamation-triangle mr-1"></i>{{ error }}
                        </p>
                    {% endfor %}
                </div>
            {% endif %}

            <div class="flex items-center justify-between pt-4">
                <a href="{% url 'dashboard:dashboard' %}" class="text-emerald-600 hover:text-emerald-500 text-sm font-medium">
                    <i class="fas fa-arrow-left mr-1"></i>
                    Back to Dashboard
                </a>
                <button type="submit" class="bg-emerald-600 text-white py-2 px-6 rounded-lg hover:bg-emerald-700 transition-colors font-medium">
                    <i class="fas fa-save mr-2"></i>
                    Change Password
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
