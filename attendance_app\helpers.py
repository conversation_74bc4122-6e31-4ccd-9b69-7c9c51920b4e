from orgunit_app.models import Department, Position
from accounts.models import Employee

'''
Retrive all employees based on the authenticated user permisions.
'''
def get_employee_list(emp_id):
    employees = None
    emp = Employee.objects.get(emp_id = emp_id)
    
    current_detp_employees = [emp]
    child_dept_emps = None
    
    if emp.position.title.title == "HR_ADMINS":
        current_detp_employees = Employee.objects.all()
        
    elif emp.position.title == "NON_MANAGERIAL":
        current_detp_employees = Employee.objects.filter(emp_id=emp.emp_id)  
        
    elif emp.position.title == "MANAGERS":
        current_detp_employees = Employee.objects.filter(department=emp.department)
                    
    elif ( emp.position.title == "CEO"
          or emp.position.title == "CXOS"
          or emp.position.title == "HEADS"
          or emp.position.title == "DIRECTORS"
          ):
        current_detp_employees = Employee.objects.filter(department=emp.department)
        
        dept_ids = []
        dept_ids.append(emp.department.id)
        cild_depts = Department.objects.filter(parent = emp.department.id)
        
        for dept in cild_depts:
           dept_ids.append(dept.id)
           
        if emp.position.title == "HEAD":
           child_dept_emps = Employee.objects.filter(department__in = dept_ids, position="MANAGER")
        if emp.position.title == "CXOS":
            child_dept_emps = Employee.objects.filter(department__in = dept_ids, position="HEAD")
        if emp.position.title == "CEO":
            child_dept_emps = Employee.objects.filter(department__in = dept_ids, position="CXOS")
    
    else:
        employees = []
    
        
    employees = current_detp_employees if child_dept_emps is None else (current_detp_employees | child_dept_emps)
    
    return employees