from orgunit_app.models import Department, Position
from accounts.models import Employee

'''
Retrive all employees based on the authenticated user permisions.
'''
def get_employee_list(emp_id):
    employees = None
    emp = Employee.objects.get(emp_id = emp_id)
    
    current_dept_employees = None # [emp]
    child_dept_emps = None
    
    if emp.position.title == "HR_ADMINS":
        current_dept_employees = Employee.objects.all()
        
    elif emp.position.title == "NON_MANAGERIAL":
        current_dept_employees = Employee.objects.filter(emp_id=emp.emp_id)  
        
    elif emp.position.title == "MANAGERS":
        current_dept_employees = Employee.objects.filter(department=emp.department)
                    
    elif ( emp.position.title == "CEO"
          or emp.position.title == "CXOS"
          or emp.position.title == "HEADS"
          or emp.position.title == "DIRECTORS"
          ):
        current_dept_employees = Employee.objects.filter(department=emp.department)
        dept_ids = []
        cild_depts = Department.objects.filter(parent = emp.department.id)
        for dept in cild_depts:
            dept_ids.append(dept.id)
           
        if emp.position.title == "HEADS":
           child_dept_emps = Employee.objects.filter(department__in = dept_ids, position=3)# position id = 3 -> HEADS
        if emp.position.title == "CXOS":
            current_dept_employees = current_dept_employees.exclude(emp_id=emp.emp_id)
            child_dept_emps = Employee.objects.filter(department__in = dept_ids, position=5) # position id = 5 -> HEADS
        if emp.position.title == "CEO":
            child_dept_emps = Employee.objects.filter(department__in = dept_ids, position="CXOS")
    
    else:
        employees = []
    
        
    employees = current_dept_employees if child_dept_emps is None else (current_dept_employees | child_dept_emps)
    
    return employees