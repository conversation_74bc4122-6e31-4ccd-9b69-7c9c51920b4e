{% extends 'base.html' %}

{% block title %}Monthly Attendance Sheet - EEU System{% endblock %}

{% block page_title %}Monthly Attendance Sheet{% endblock %}
{% block page_subtitle %}{{ month_name }} {{ year }}{% if selected_employee %} - {{ selected_employee.Fname }} {{ selected_employee.Lname }}{% endif %}{% endblock %}

{% block extra_css %}
<style>
    .calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;
        background-color: #e5e7eb;
        border: 1px solid #e5e7eb;
    }
    
    .calendar-day {
        background-color: white;
        min-height: 80px;
        padding: 8px;
        position: relative;
        transition: all 0.2s;
    }
    
    .calendar-day:hover {
        background-color: #f9fafb;
    }
    
    .calendar-day.weekend {
        background-color: #f3f4f6;
        border-right: 3px solid #6b7280;
    }
    
    .calendar-day.empty {
        background-color: #f9fafb;
    }
    
    .day-number {
        font-weight: bold;
        font-size: 1.1rem;
        margin-bottom: 2px;
    }
    
    .day-name {
        font-size: 0.7rem;
        color: #6b7280;
        margin-bottom: 4px;
    }
    
    .attendance-status {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: bold;
        color: white;
        margin-top: 4px;
    }
    
    .status-P { background-color: #10b981; } /* Present - Green */
    .status-A { background-color: #ef4444; } /* Absent - Red */
    .status-LV { background-color: #3b82f6; } /* Leave - Blue */
    .status-RM { background-color: #f59e0b; } /* Remark - Yellow */
    .status-HD { background-color: #6b7280; } /* Holiday - Gray */
    
    .tooltip {
        position: absolute;
        background-color: #1f2937;
        color: white;
        padding: 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        z-index: 1000;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s;
    }
    
    .calendar-day:hover .tooltip {
        opacity: 1;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        margin-right: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .legend-color {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        margin-right: 8px;
    }
</style>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header Section with Employee Selection and Navigation -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <!-- Employee Information -->
            <div>
                {% if selected_employee %}
                    <h2 class="text-xl font-semibold text-gray-900">
                        {{ selected_employee.Fname }} {{ selected_employee.Lname }}
                    </h2>
                    <p class="text-sm text-gray-600">
                        {% if selected_employee.department %}
                            {{ selected_employee.department.name }} Department
                        {% endif %}
                        {% if selected_employee.position %}
                            - {{ selected_employee.position.title }}
                        {% endif %}
                    </p>
                {% else %}
                    <h2 class="text-xl font-semibold text-gray-900">Attendance Sheet</h2>
                {% endif %}
            </div>
            
            <!-- Employee Selection (for managers and HR) -->
            {% if can_select_employee %}
                <div class="flex items-center space-x-4">
                    <label for="employee-select" class="text-sm font-medium text-gray-700">Select Employee:</label>
                    <select id="employee-select" onchange="changeEmployee()" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        {% for employee in available_employees %}
                            <option value="{{ employee.id }}" {% if employee.id == selected_employee.id %}selected{% endif %}>
                                {{ employee.Fname }} {{ employee.Lname }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Month Navigation -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
            <a href="?month={{ prev_month }}&year={{ prev_year }}{% if selected_employee %}&employee_id={{ selected_employee.id }}{% endif %}" 
               class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                <i class="fas fa-chevron-left mr-2"></i>
                Previous Month
            </a>
            
            <h3 class="text-2xl font-bold text-gray-900">{{ month_name }} {{ year }}</h3>
            
            <a href="?month={{ next_month }}&year={{ next_year }}{% if selected_employee %}&employee_id={{ selected_employee.id }}{% endif %}" 
               class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                Next Month
                <i class="fas fa-chevron-right ml-2"></i>
            </a>
        </div>
    </div>

    <!-- Legend -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h4 class="text-lg font-semibold text-gray-900 mb-4">Attendance Legend</h4>
        <div class="flex flex-wrap">
            <div class="legend-item">
                <div class="legend-color status-P"></div>
                <span class="text-sm text-gray-700">Present (P)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-A"></div>
                <span class="text-sm text-gray-700">Absent (A)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-LV"></div>
                <span class="text-sm text-gray-700">Leave (LV)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-RM"></div>
                <span class="text-sm text-gray-700">Remark (RM)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-HD"></div>
                <span class="text-sm text-gray-700">Holiday (HD)</span>
            </div>
        </div>
    </div>

    <!-- Calendar Grid -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <!-- Calendar Header -->
        <div class="calendar-grid mb-1">
            <div class="text-center font-semibold text-gray-700 py-3 bg-gray-50">Sun</div>
            <div class="text-center font-semibold text-gray-700 py-3 bg-gray-50">Mon</div>
            <div class="text-center font-semibold text-gray-700 py-3 bg-gray-50">Tue</div>
            <div class="text-center font-semibold text-gray-700 py-3 bg-gray-50">Wed</div>
            <div class="text-center font-semibold text-gray-700 py-3 bg-gray-50">Thu</div>
            <div class="text-center font-semibold text-gray-700 py-3 bg-gray-50">Fri</div>
            <div class="text-center font-semibold text-gray-700 py-3 bg-gray-50">Sat</div>
        </div>

        <!-- Calendar Body -->
        <div class="calendar-grid">
            {% for week in calendar_data %}
                {% for day in week %}
                    {% if day == 0 %}
                        <div class="calendar-day empty"></div>
                    {% else %}
                        <div class="calendar-day" data-day="{{ day }}">
                            <div class="day-number">{{ day }}</div>
                            <div class="day-name" id="day-name-{{ day }}"></div>
                            <div id="attendance-{{ day }}"></div>
                        </div>
                    {% endif %}
                {% endfor %}
            {% endfor %}
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h4 class="text-lg font-semibold text-gray-900 mb-4">Monthly Summary</h4>
        <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
            {% with attendance_data.values as days %}
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">
                        {{ days|length|add:"-"|add:days|length }}
                    </div>
                    <div class="text-sm text-gray-600">Present Days</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-600">0</div>
                    <div class="text-sm text-gray-600">Absent Days</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">0</div>
                    <div class="text-sm text-gray-600">Leave Days</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-600">0</div>
                    <div class="text-sm text-gray-600">Special Cases</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-600">0</div>
                    <div class="text-sm text-gray-600">Holidays</div>
                </div>
            {% endwith %}
        </div>
    </div>
</div>

<script>
// Attendance data from Django
const attendanceData = {{ attendance_data_json|safe }};
const month = {{ month }};
const year = {{ year }};

// Day names
const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

// Populate calendar with attendance data
document.addEventListener('DOMContentLoaded', function() {
    // Populate day names and attendance data
    for (let day = 1; day <= 31; day++) {
        const dayElement = document.querySelector(`[data-day="${day}"]`);
        if (!dayElement) continue;

        const date = new Date(year, month - 1, day);
        if (date.getMonth() !== month - 1) continue; // Skip invalid dates

        const dayNameElement = document.getElementById(`day-name-${day}`);
        const attendanceElement = document.getElementById(`attendance-${day}`);

        if (dayNameElement) {
            dayNameElement.textContent = dayNames[date.getDay()];
        }

        // Add weekend class
        if (date.getDay() === 0) { // Sunday
            dayElement.classList.add('weekend');
        }

        // Add attendance data if available
        if (attendanceData && attendanceData[day]) {
            const dayData = attendanceData[day];

            const statusElement = document.createElement('div');
            statusElement.className = `attendance-status status-${dayData.status}`;
            statusElement.textContent = dayData.status;
            attendanceElement.appendChild(statusElement);

            // Add tooltip
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';

            if (dayData.check_in && dayData.check_out) {
                tooltip.innerHTML = `Check-in: ${dayData.check_in}<br>Check-out: ${dayData.check_out}`;
            } else if (dayData.status === 'HD') {
                tooltip.textContent = 'Holiday';
            } else if (dayData.status === 'LV') {
                tooltip.textContent = 'On Leave';
            } else if (dayData.status === 'A') {
                tooltip.textContent = 'Absent';
            } else if (dayData.status === 'RM') {
                let tooltipText = 'Special Case';
                if (dayData.check_in) tooltipText += `\nIn: ${dayData.check_in}`;
                if (dayData.check_out) tooltipText += `\nOut: ${dayData.check_out}`;
                tooltip.innerHTML = tooltipText.replace(/\n/g, '<br>');
            }

            dayElement.appendChild(tooltip);
        }
    }
});

function changeEmployee() {
    const select = document.getElementById('employee-select');
    const employeeId = select.value;
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('employee_id', employeeId);
    window.location.href = currentUrl.toString();
}
</script>
{% endblock %}
