{% extends 'base.html' %}

{% block title %}Monthly Attendance Sheet - EEU System{% endblock %}

{% block page_title %}Monthly Attendance Sheet{% endblock %}
{% block page_subtitle %}{{ page_subtitle }}{% endblock %}

{% block extra_css %}
<style>
    /* Overall page background */
    body {
        background-color: #f8fafc;
    }

    .attendance-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.875rem;
        background-color: #ffffff;
    }

    .attendance-table th,
    .attendance-table td {
        border: 1px solid #d1d5db;
        padding: 6px 4px;
        text-align: center;
        vertical-align: middle;
        background-color: #ffffff;
    }

    .attendance-table th {
        background-color: #f1f5f9;
        font-weight: 600;
        color: #1f2937;
        position: sticky;
        top: 0;
        z-index: 10;
        border-bottom: 2px solid #d1d5db;
    }

    /* Make the first header column (Employee) sticky both horizontally and vertically */
    .attendance-table th.employee-info {
        position: sticky;
        left: 0;
        top: 0;
        z-index: 15;
        background-color: #f1f5f9;
        border-right: 2px solid #cbd5e1;
    }

    .employee-info {
        background-color: #f1f5f9;
        text-align: left;
        padding: 8px 8px;
        min-width: 200px;
        position: sticky;
        left: 0;
        z-index: 5;
        border-right: 2px solid #cbd5e1;
    }

    /* Ensure employee info cells have proper background */
    .attendance-table tbody .employee-info {
        background-color: #ffffff;
    }

    .employee-name {
        font-weight: 600;
        color: #111827;
        margin-bottom: 2px;
        text-align: left;
    }

    .employee-details {
        font-size: 0.75rem;
        color: #6b7280;
    }

    .day-header {
        min-width: 35px;
        padding: 8px 4px;
    }

    .day-header.weekend {
        background-color: #fef3c7;
        border-right: 3px solid #f59e0b;
    }

    .day-header.today {
        background-color: #d1fae5;
        border: 2px solid #10b981;
        font-weight: bold;
        box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
    }

    .day-header.today .day-number {
        color: #047857;
        font-weight: 800;
    }

    .day-header.today .day-name {
        color: #065f46;
        font-weight: 600;
    }

    .day-number {
        font-weight: bold;
        margin-bottom: 2px;
    }

    .day-name {
        font-size: 0.6rem;
        color: #6b7280;
    }

    .attendance-cell {
        position: relative;
        min-width: 35px;
        height: 32px;
        cursor: pointer;
    }

    .attendance-cell.weekend {
        background-color: #fefce8 !important;
        border-right: 3px solid #f59e0b;
    }

    /* Ensure weekend styling takes precedence over status colors */
    .attendance-cell.weekend.status-P,
    .attendance-cell.weekend.status-A,
    .attendance-cell.weekend.status-LV,
    .attendance-cell.weekend.status-RM,
    .attendance-cell.weekend.status-HD {
        background-color: #fefce8 !important;
    }

   /* .attendance-cell.today {
        background-color: #ecfdf5;
        border-left: 2px solid #10b981;
        border-right: 2px solid #10b981;
        box-shadow: inset 0 0 0 1px #10b981;
    } */

    .attendance-status {
        font-size: 0.85rem;
        font-weight: 700;
        text-align: center;
        display: block;
        width: 100%;
        height: 100%;
        line-height: 1.2;
    }

    /* Cell background colors based on attendance status */
    .attendance-cell.status-P {
        background-color: #dcfce7;
        color: #166534;
    } /* Present - Light green background, dark green text */

    .attendance-cell.status-A {
        background-color: #fecaca;
        color: #991b1b;
    } /* Absent - Light red background, dark red text */

    .attendance-cell.status-LV {
        background-color: #dbeafe;
        color: #1e40af;
    } /* Leave - Light blue background, dark blue text */

    .attendance-cell.status-RM {
        background-color: #fef3c7;
        color: #92400e;
    } /* Remark - Light yellow background, dark yellow/orange text */

    .attendance-cell.status-HD {
        background-color: #f3f4f6;
        color: #374151;
    } /* Holiday - Light gray background, dark gray text */

    .tooltip {
        position: absolute;
        background-color: #1f2937;
        color: white;
        padding: 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        z-index: 1000;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-bottom: 5px;
    }

    .tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #1f2937 transparent transparent transparent;
    }

    .attendance-cell:hover .tooltip {
        opacity: 1;
    }

    .legend-item {
        display: flex;
        align-items: center;
        margin-right: 1.5rem;
        margin-bottom: 0.75rem;
    }

    .legend-color {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        margin-right: 10px;
        border: 1px solid #d1d5db;
        flex-shrink: 0;
    }

    /* Legend colors matching the new cell background colors */
    .legend-color.status-P {
        background-color: #dcfce7;
        border-color: #166534;
    }

    .legend-color.status-A {
        background-color: #fecaca;
        border-color: #991b1b;
    }

    .legend-color.status-LV {
        background-color: #dbeafe;
        border-color: #1e40af;
    }

    .legend-color.status-RM {
        background-color: #fef3c7;
        border-color: #92400e;
    }

    .legend-color.status-HD {
        background-color: #f3f4f6;
        border-color: #374151;
    }

    .legend-text {
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
    }

    /* Department dropdown styling */
    #department-select {
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.5rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: 2.5rem;
    }

    #department-select:focus {
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }

    #department-select option {
        padding: 0.5rem;
        background-color: #ffffff;
        color: #111827;
    }

    .table-container {
        overflow-x: auto;
        max-width: 100%;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        background-color: #ffffff;
    }

    @media (max-width: 768px) {
        .attendance-table {
            font-size: 0.75rem;
        }

        .attendance-table th,
        .attendance-table td {
            padding: 4px 2px;
        }

        .employee-info {
            min-width: 150px;
            padding: 6px 6px;
        }

        .day-header {
            min-width: 30px;
        }

        .attendance-cell {
            min-width: 30px;
            height: 28px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="space-y-3" style="background-color: #f8fafc; min-height: 100vh; padding: 1rem 0;">
    <!-- Header Section with Department Filter and Navigation -->
    <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <!-- Sheet Information -->
            <div>
                <h2 class="text-xl font-semibold text-gray-900">
                    {% if user_role == 'NON_MANAGERIAL' %}
                        My Attendance Sheet
                    {% elif user_role == 'MANAGERS' %}
                        Department Attendance Sheet
                    {% else %}
                        Company Attendance Sheet
                    {% endif %}
                </h2>
                <p class="text-sm text-gray-600">
                    {% if selected_department %}
                        {{ selected_department.dept_name }} Department
                    {% elif user_role == 'HR_ADMINS' %}
                        All Departments
                    {% endif %}
                    - {{ employees_attendance_data|length }} employee{{ employees_attendance_data|length|pluralize }}
                </p>
            </div>

            <!-- Department Filter (for HR_ADMINS only) -->
            {% if can_filter_department %}
                <div class="flex items-center space-x-4">
                    <label for="department-select" class="text-sm font-medium text-gray-700">Filter by Department:</label>
                    <select id="department-select" onchange="changeDepartment()" class="border border-gray-300 rounded-md px-3 py-2 text-sm bg-white text-gray-900 font-medium min-w-48 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 shadow-sm">
                        <option value="" class="text-gray-900 font-medium">All Departments</option>
                        {% for department in available_departments %}
                            <option value="{{ department.id }}" class="text-gray-900 font-medium" {% if selected_department and department.id == selected_department.id %}selected{% endif %}>
                                {{ department.dept_name|default:department.dept_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Month Navigation -->
    <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div class="flex items-center justify-between">
            <a href="?month={{ prev_month }}&year={{ prev_year }}{% if selected_department %}&department_id={{ selected_department.id }}{% endif %}"
               class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                <i class="fas fa-chevron-left mr-2"></i>
                Previous Month
            </a>

            <h3 class="text-lg font-bold text-gray-900">{{ month_name }} {{ year }}</h3>

            <a href="?month={{ next_month }}&year={{ next_year }}{% if selected_department %}&department_id={{ selected_department.id }}{% endif %}"
               class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                Next Month
                <i class="fas fa-chevron-right ml-2"></i>
            </a>
        </div>
    </div>



    <!-- Attendance Table -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="table-container">
            <table class="attendance-table">
                <!-- Table Header -->
                <thead>
                    <tr>
                        <th class="employee-info">Employee [ID]</th>
                        {% for day_header in day_headers %}
                            <th class="day-header {% if day_header.is_weekend %}weekend{% endif %} {% if day_header.is_today %}today{% endif %}">
                                <div class="day-number">{{ day_header.day }}</div>
                                <div class="day-name">{{ day_header.day_name }}</div>
                                {% if day_header.is_today %}
                                    <div style="font-size: 0.6rem; color: #059669; font-weight: bold;">TODAY</div>
                                {% endif %}
                            </th>
                        {% endfor %}
                    </tr>
                </thead>

                <!-- Table Body -->
                <tbody>
                    {% for employee_data in employees_attendance_data %}
                        <tr>
                            <!-- Employee Information Column -->
                            <td class="employee-info">
                                <div class="employee-name">
                                    {{ employee_data.employee.Fname }} {{ employee_data.employee.Lname }}{% if employee_data.employee.emp_id %} [{{ employee_data.employee.emp_id }}]{% endif %}
                                </div>
                            </td>

                            <!-- Attendance Data Columns -->
                            {% for day_header in day_headers %}
                                {% with employee_data.attendance_data|default_if_none:''|add:day_header.day|default:'' as day_data %}
                                    <td class="attendance-cell {% if day_header.is_weekend %}weekend{% endif %} {% if day_header.is_today %}today{% endif %}"
                                        data-employee="{{ employee_data.employee.id }}"
                                        data-day="{{ day_header.day }}">

                                        <div id="attendance-{{ employee_data.employee.id }}-{{ day_header.day }}">
                                            <!-- Attendance status will be populated by JavaScript -->
                                        </div>
                                    </td>
                                {% endwith %}
                            {% endfor %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if not employees_attendance_data %}
            <div class="text-center py-12">
                <i class="fas fa-calendar-times text-4xl text-gray-300 mb-4"></i>
                <p class="text-gray-500 text-lg">No employees found</p>
                <p class="text-gray-400 text-sm">
                    {% if user_role == 'NON_MANAGERIAL' %}
                        Your employee profile may not be set up correctly.
                    {% elif can_filter_department %}
                        Try selecting a different department or view all departments.
                    {% else %}
                        No employees are assigned to your department.
                    {% endif %}
                </p>
            </div>
        {% endif %}
    </div>

    <!-- Legend -->
    <div class="bg-white rounded-lg shadow-sm p-2 border border-gray-200">
        <h4 class="text-lg font-semibold text-gray-900 mb-4">Attendance Legend</h4>
        <div class="flex flex-wrap items-center">
            <div class="legend-item">
                <div class="legend-color status-P"></div>
                <span class="legend-text">Present (P)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-A"></div>
                <span class="legend-text">Absent (A)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-LV"></div>
                <span class="legend-text">Leave (LV)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-RM"></div>
                <span class="legend-text">Remark (RM)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-HD"></div>
                <span class="legend-text">Holiday (HD)</span>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    {% if employees_attendance_data %}
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h4 class="text-lg font-semibold text-gray-900 mb-4">Monthly Summary</h4>
            <div class="grid grid-cols-2 md:grid-cols-5 gap-6">
                <div class="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                    <div class="text-2xl font-bold text-green-700" id="total-present">0</div>
                    <div class="text-sm font-medium text-green-600">Total Present</div>
                </div>
                <div class="text-center p-4 bg-red-50 rounded-lg border border-red-200">
                    <div class="text-2xl font-bold text-red-700" id="total-absent">0</div>
                    <div class="text-sm font-medium text-red-600">Total Absent</div>
                </div>
                <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div class="text-2xl font-bold text-blue-700" id="total-leave">0</div>
                    <div class="text-sm font-medium text-blue-600">Total Leave</div>
                </div>
                <div class="text-center p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div class="text-2xl font-bold text-yellow-700" id="total-remark">0</div>
                    <div class="text-sm font-medium text-yellow-600">Special Cases</div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <div class="text-2xl font-bold text-gray-700" id="total-holiday">0</div>
                    <div class="text-sm font-medium text-gray-600">Holidays</div>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<script>
// Attendance data from Django
const attendanceData = {{ employees_attendance_json|safe }};
const month = {{ month }};
const year = {{ year }};
const daysInMonth = {{ days_in_month }};

// Statistics counters
let totalPresent = 0, totalAbsent = 0, totalLeave = 0, totalRemark = 0, totalHoliday = 0;

// Populate table with attendance data
document.addEventListener('DOMContentLoaded', function() {
    console.log('Attendance data:', attendanceData); // Debug log

    // Populate attendance cells
    for (let employeeId in attendanceData) {
        const employeeData = attendanceData[employeeId];
        console.log(`Processing employee ${employeeId}:`, employeeData); // Debug log

        for (let day = 1; day <= daysInMonth; day++) {
            const cellElement = document.getElementById(`attendance-${employeeId}-${day}`);
            if (!cellElement) {
                console.log(`Cell not found: attendance-${employeeId}-${day}`); // Debug log
                continue;
            }

            const dayData = employeeData[day];
            if (!dayData || !dayData.status) {
                console.log(`No data or status for employee ${employeeId}, day ${day}`); // Debug log
                continue;
            }

            console.log(`Adding status ${dayData.status} for employee ${employeeId}, day ${day}`); // Debug log

            // Apply status class to the cell itself
            const parentCell = cellElement.parentElement;
            parentCell.classList.add(`status-${dayData.status}`);

            // Add status text directly to the cell content
            const statusElement = document.createElement('div');
            statusElement.className = 'attendance-status';
            statusElement.textContent = dayData.status;
            cellElement.appendChild(statusElement);

            // Create tooltip
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';

            if (dayData.check_in && dayData.check_out) {
                tooltip.innerHTML = `Check-in: ${dayData.check_in}<br>Check-out: ${dayData.check_out}`;
            } else if (dayData.status === 'HD') {
                tooltip.textContent = 'Holiday';
            } else if (dayData.status === 'LV') {
                tooltip.textContent = 'On Leave';
            } else if (dayData.status === 'A') {
                tooltip.textContent = 'Absent';
            } else if (dayData.status === 'RM') {
                let tooltipText = 'Special Case';
                if (dayData.check_in) tooltipText += `<br>In: ${dayData.check_in}`;
                if (dayData.check_out) tooltipText += `<br>Out: ${dayData.check_out}`;
                tooltip.innerHTML = tooltipText;
            }

            parentCell.appendChild(tooltip);

            // Update statistics
            switch (dayData.status) {
                case 'P': totalPresent++; break;
                case 'A': totalAbsent++; break;
                case 'LV': totalLeave++; break;
                case 'RM': totalRemark++; break;
                case 'HD': totalHoliday++; break;
            }
        }
    }

    console.log('Statistics:', {totalPresent, totalAbsent, totalLeave, totalRemark, totalHoliday}); // Debug log

    // Update summary statistics
    const presentEl = document.getElementById('total-present');
    const absentEl = document.getElementById('total-absent');
    const leaveEl = document.getElementById('total-leave');
    const remarkEl = document.getElementById('total-remark');
    const holidayEl = document.getElementById('total-holiday');

    if (presentEl) presentEl.textContent = totalPresent;
    if (absentEl) absentEl.textContent = totalAbsent;
    if (leaveEl) leaveEl.textContent = totalLeave;
    if (remarkEl) remarkEl.textContent = totalRemark;
    if (holidayEl) holidayEl.textContent = totalHoliday;
});

function changeDepartment() {
    const select = document.getElementById('department-select');
    const departmentId = select.value;
    const currentUrl = new URL(window.location);

    if (departmentId) {
        currentUrl.searchParams.set('department_id', departmentId);
    } else {
        currentUrl.searchParams.delete('department_id');
    }

    window.location.href = currentUrl.toString();
}
</script>
{% endblock %}
