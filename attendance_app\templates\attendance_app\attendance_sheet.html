{% extends 'base.html' %}

{% block title %}Monthly Attendance Sheet - EEU System{% endblock %}

{% block page_title %}Monthly Attendance Sheet{% endblock %}
{% block page_subtitle %}{{ page_subtitle }}{% endblock %}

{% block extra_css %}
<style>
    .attendance-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.875rem;
    }

    .attendance-table th,
    .attendance-table td {
        border: 1px solid #e5e7eb;
        padding: 8px 4px;
        text-align: center;
        vertical-align: middle;
    }

    .attendance-table th {
        background-color: #f9fafb;
        font-weight: 600;
        color: #374151;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .employee-info {
        background-color: #f9fafb;
        text-align: left;
        padding: 12px 8px;
        min-width: 200px;
        position: sticky;
        left: 0;
        z-index: 5;
        border-right: 2px solid #d1d5db;
    }

    .employee-name {
        font-weight: 600;
        color: #111827;
        margin-bottom: 2px;
    }

    .employee-details {
        font-size: 0.75rem;
        color: #6b7280;
    }

    .day-header {
        min-width: 35px;
        padding: 8px 4px;
    }

    .day-header.weekend {
        background-color: #fef3c7;
        border-right: 3px solid #f59e0b;
    }

    .day-header.today {
        background-color: #dcfce7;
        border: 2px solid #10b981;
        font-weight: bold;
    }

    .day-header.today .day-number {
        color: #059669;
    }

    .day-header.today .day-name {
        color: #047857;
    }

    .day-number {
        font-weight: bold;
        margin-bottom: 2px;
    }

    .day-name {
        font-size: 0.6rem;
        color: #6b7280;
    }

    .attendance-cell {
        position: relative;
        min-width: 35px;
        height: 40px;
        cursor: pointer;
    }

    .attendance-cell.weekend {
        background-color: #fffbeb;
        border-right: 3px solid #f59e0b;
    }

    .attendance-cell.today {
        background-color: #f0fdf4;
        border-left: 2px solid #10b981;
        border-right: 2px solid #10b981;
    }

    .attendance-status {
        display: inline-block;
        padding: 4px 6px;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: bold;
        color: white;
        min-width: 24px;
    }

    .status-P { background-color: #10b981; } /* Present - Green */
    .status-A { background-color: #ef4444; } /* Absent - Red */
    .status-LV { background-color: #3b82f6; } /* Leave - Blue */
    .status-RM { background-color: #f59e0b; } /* Remark - Yellow */
    .status-HD { background-color: #6b7280; } /* Holiday - Gray */

    .tooltip {
        position: absolute;
        background-color: #1f2937;
        color: white;
        padding: 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        z-index: 1000;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-bottom: 5px;
    }

    .tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #1f2937 transparent transparent transparent;
    }

    .attendance-cell:hover .tooltip {
        opacity: 1;
    }

    .legend-item {
        display: flex;
        align-items: center;
        margin-right: 1rem;
        margin-bottom: 0.5rem;
    }

    .legend-color {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        margin-right: 8px;
    }

    .table-container {
        overflow-x: auto;
        max-width: 100%;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
    }

    @media (max-width: 768px) {
        .attendance-table {
            font-size: 0.75rem;
        }

        .attendance-table th,
        .attendance-table td {
            padding: 4px 2px;
        }

        .employee-info {
            min-width: 150px;
            padding: 8px 6px;
        }

        .day-header {
            min-width: 30px;
        }

        .attendance-cell {
            min-width: 30px;
            height: 35px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header Section with Department Filter and Navigation -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <!-- Sheet Information -->
            <div>
                <h2 class="text-xl font-semibold text-gray-900">
                    {% if user_role == 'NON_MANAGERIAL' %}
                        My Attendance Sheet
                    {% elif user_role == 'MANAGER' %}
                        Department Attendance Sheet
                    {% else %}
                        Company Attendance Sheet
                    {% endif %}
                </h2>
                <p class="text-sm text-gray-600">
                    {% if selected_department %}
                        {{ selected_department.name }} Department
                    {% elif user_role == 'HR_ADMINS' %}
                        All Departments
                    {% endif %}
                    - {{ employees_attendance_data|length }} employee{{ employees_attendance_data|length|pluralize }}
                </p>
            </div>

            <!-- Department Filter (for HR_ADMINS only) -->
            {% if can_filter_department %}
                <div class="flex items-center space-x-4">
                    <label for="department-select" class="text-sm font-medium text-gray-700">Filter by Department:</label>
                    <select id="department-select" onchange="changeDepartment()" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="">All Departments</option>
                        {% for department in available_departments %}
                            <option value="{{ department.id }}" {% if selected_department and department.id == selected_department.id %}selected{% endif %}>
                                {{ department.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Month Navigation -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
            <a href="?month={{ prev_month }}&year={{ prev_year }}{% if selected_department %}&department_id={{ selected_department.id }}{% endif %}"
               class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                <i class="fas fa-chevron-left mr-2"></i>
                Previous Month
            </a>

            <h3 class="text-2xl font-bold text-gray-900">{{ month_name }} {{ year }}</h3>

            <a href="?month={{ next_month }}&year={{ next_year }}{% if selected_department %}&department_id={{ selected_department.id }}{% endif %}"
               class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                Next Month
                <i class="fas fa-chevron-right ml-2"></i>
            </a>
        </div>
    </div>

    <!-- Legend -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h4 class="text-lg font-semibold text-gray-900 mb-4">Attendance Legend</h4>
        <div class="flex flex-wrap">
            <div class="legend-item">
                <div class="legend-color status-P"></div>
                <span class="text-sm text-gray-700">Present (P)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-A"></div>
                <span class="text-sm text-gray-700">Absent (A)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-LV"></div>
                <span class="text-sm text-gray-700">Leave (LV)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-RM"></div>
                <span class="text-sm text-gray-700">Remark (RM)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-HD"></div>
                <span class="text-sm text-gray-700">Holiday (HD)</span>
            </div>
        </div>
    </div>

    <!-- Attendance Table -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="table-container">
            <table class="attendance-table">
                <!-- Table Header -->
                <thead>
                    <tr>
                        <th class="employee-info">Employee</th>
                        {% for day_header in day_headers %}
                            <th class="day-header {% if day_header.is_weekend %}weekend{% endif %} {% if day_header.is_today %}today{% endif %}">
                                <div class="day-number">{{ day_header.day }}</div>
                                <div class="day-name">{{ day_header.day_name }}</div>
                                {% if day_header.is_today %}
                                    <div style="font-size: 0.6rem; color: #059669; font-weight: bold;">TODAY</div>
                                {% endif %}
                            </th>
                        {% endfor %}
                    </tr>
                </thead>

                <!-- Table Body -->
                <tbody>
                    {% for employee_data in employees_attendance_data %}
                        <tr>
                            <!-- Employee Information Column -->
                            <td class="employee-info">
                                <div class="employee-name">
                                    {{ employee_data.employee.Fname }} {{ employee_data.employee.Lname }}
                                </div>
                                <div class="employee-details">
                                    {% if employee_data.employee.department %}
                                        {{ employee_data.employee.department.name }}
                                    {% endif %}
                                    {% if employee_data.employee.position %}
                                        <br>{{ employee_data.employee.position.title }}
                                    {% endif %}
                                </div>
                            </td>

                            <!-- Attendance Data Columns -->
                            {% for day_header in day_headers %}
                                {% with employee_data.attendance_data|default_if_none:''|add:day_header.day|default:'' as day_data %}
                                    <td class="attendance-cell {% if day_header.is_weekend %}weekend{% endif %} {% if day_header.is_today %}today{% endif %}"
                                        data-employee="{{ employee_data.employee.id }}"
                                        data-day="{{ day_header.day }}">

                                        <div id="attendance-{{ employee_data.employee.id }}-{{ day_header.day }}">
                                            <!-- Attendance status will be populated by JavaScript -->
                                        </div>
                                    </td>
                                {% endwith %}
                            {% endfor %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if not employees_attendance_data %}
            <div class="text-center py-12">
                <i class="fas fa-calendar-times text-4xl text-gray-300 mb-4"></i>
                <p class="text-gray-500 text-lg">No employees found</p>
                <p class="text-gray-400 text-sm">
                    {% if user_role == 'NON_MANAGERIAL' %}
                        Your employee profile may not be set up correctly.
                    {% elif can_filter_department %}
                        Try selecting a different department or view all departments.
                    {% else %}
                        No employees are assigned to your department.
                    {% endif %}
                </p>
            </div>
        {% endif %}
    </div>

    <!-- Summary Statistics -->
    {% if employees_attendance_data %}
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h4 class="text-lg font-semibold text-gray-900 mb-4">Monthly Summary</h4>
            <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600" id="total-present">0</div>
                    <div class="text-sm text-gray-600">Total Present</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-600" id="total-absent">0</div>
                    <div class="text-sm text-gray-600">Total Absent</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600" id="total-leave">0</div>
                    <div class="text-sm text-gray-600">Total Leave</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-600" id="total-remark">0</div>
                    <div class="text-sm text-gray-600">Special Cases</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-600" id="total-holiday">0</div>
                    <div class="text-sm text-gray-600">Holidays</div>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<script>
// Attendance data from Django
const employeesAttendanceData = {{ employees_attendance_data|safe }};
const month = {{ month }};
const year = {{ year }};
const daysInMonth = {{ days_in_month }};

// Statistics counters
let totalPresent = 0, totalAbsent = 0, totalLeave = 0, totalRemark = 0, totalHoliday = 0;

// Populate table with attendance data
document.addEventListener('DOMContentLoaded', function() {
    // Convert Django data to JavaScript format
    const attendanceData = {};

    {% for employee_data in employees_attendance_data %}
        attendanceData[{{ employee_data.employee.id }}] = {
            {% for day, data in employee_data.attendance_data.items %}
                {{ day }}: {
                    status: '{{ data.status }}',
                    check_in: {% if data.check_in %}'{{ data.check_in }}'{% else %}null{% endif %},
                    check_out: {% if data.check_out %}'{{ data.check_out }}'{% else %}null{% endif %},
                    is_weekend: {{ data.is_weekend|yesno:"true,false" }}
                },
            {% endfor %}
        };
    {% endfor %}

    // Populate attendance cells
    for (let employeeId in attendanceData) {
        const employeeData = attendanceData[employeeId];

        for (let day = 1; day <= daysInMonth; day++) {
            const cellElement = document.getElementById(`attendance-${employeeId}-${day}`);
            if (!cellElement) continue;

            const dayData = employeeData[day];
            if (!dayData) continue;

            // Create status element
            const statusElement = document.createElement('div');
            statusElement.className = `attendance-status status-${dayData.status}`;
            statusElement.textContent = dayData.status;
            cellElement.appendChild(statusElement);

            // Create tooltip
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';

            if (dayData.check_in && dayData.check_out) {
                tooltip.innerHTML = `Check-in: ${dayData.check_in}<br>Check-out: ${dayData.check_out}`;
            } else if (dayData.status === 'HD') {
                tooltip.textContent = 'Holiday';
            } else if (dayData.status === 'LV') {
                tooltip.textContent = 'On Leave';
            } else if (dayData.status === 'A') {
                tooltip.textContent = 'Absent';
            } else if (dayData.status === 'RM') {
                let tooltipText = 'Special Case';
                if (dayData.check_in) tooltipText += `<br>In: ${dayData.check_in}`;
                if (dayData.check_out) tooltipText += `<br>Out: ${dayData.check_out}`;
                tooltip.innerHTML = tooltipText;
            }

            cellElement.parentElement.appendChild(tooltip);

            // Update statistics
            switch (dayData.status) {
                case 'P': totalPresent++; break;
                case 'A': totalAbsent++; break;
                case 'LV': totalLeave++; break;
                case 'RM': totalRemark++; break;
                case 'HD': totalHoliday++; break;
            }
        }
    }

    // Update summary statistics
    document.getElementById('total-present').textContent = totalPresent;
    document.getElementById('total-absent').textContent = totalAbsent;
    document.getElementById('total-leave').textContent = totalLeave;
    document.getElementById('total-remark').textContent = totalRemark;
    document.getElementById('total-holiday').textContent = totalHoliday;
});

function changeDepartment() {
    const select = document.getElementById('department-select');
    const departmentId = select.value;
    const currentUrl = new URL(window.location);

    if (departmentId) {
        currentUrl.searchParams.set('department_id', departmentId);
    } else {
        currentUrl.searchParams.delete('department_id');
    }

    window.location.href = currentUrl.toString();
}
</script>
{% endblock %}
