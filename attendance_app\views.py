from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import Http404
from accounts.models import Employee
from orgunit_app.models import Department
from datetime import datetime, timedelta
import calendar
import random
import json

@login_required
def index(request):
    """
    Attendance app index view.
    Displays the main attendance management interface.
    """
    context = {
        'page_title': 'Attendance Management',
        'page_subtitle': 'Track and manage employee attendance',
    }
    return render(request, 'attendance_app/index.html', context)

@login_required
def attendance_sheet_view(request):
    """
    Monthly attendance sheet view with table-based layout and role-based access control.
    """
    # Get month, year, and department filter from request parameters
    month = int(request.GET.get('month', datetime.now().month))
    year = int(request.GET.get('year', datetime.now().year))
    department_id = request.GET.get('department_id')

    # Validate month and year
    if month < 1 or month > 12:
        month = datetime.now().month
    if year < 2020 or year > 2030:
        year = datetime.now().year

    # Determine employee access based on role
    current_user = request.user
    employees_to_display = []
    available_departments = []
    selected_department = None
    user_role = 'NON_MANAGERIAL'  # Default role

    # Role-based access control
    if current_user.is_superuser or (hasattr(current_user, 'employee_profile') and
                                   current_user.employee_profile and
                                   current_user.employee_profile.position and
                                   current_user.employee_profile.position.title == 'HR_ADMINS'):
        # HR_ADMINS and Superusers can view all employees across all departments
        user_role = 'HR_ADMINS'
        from orgunit_app.models import Department
        available_departments = Department.objects.all().order_by('name')

        if department_id:
            selected_department = get_object_or_404(Department, id=department_id)
            employees_to_display = Employee.objects.filter(department=selected_department).order_by('Fname', 'Lname')
        else:
            # Show all employees if no department filter
            employees_to_display = Employee.objects.all().order_by('department__name', 'Fname', 'Lname')

    elif (hasattr(current_user, 'employee_profile') and
          current_user.employee_profile and
          current_user.employee_profile.position and
          'MANAGER' in current_user.employee_profile.position.title.upper()):
        # Managers can view all employees in their department only
        user_role = 'MANAGER'
        user_department = current_user.employee_profile.department
        selected_department = user_department
        employees_to_display = Employee.objects.filter(department=user_department).order_by('Fname', 'Lname')

    else:
        # NON_MANAGERIAL employees can only view their own attendance
        user_role = 'NON_MANAGERIAL'
        if hasattr(current_user, 'employee_profile') and current_user.employee_profile:
            employees_to_display = [current_user.employee_profile]
        else:
            raise Http404("Employee profile not found.")

    # Generate calendar data
    month_name = calendar.month_name[month]
    days_in_month = calendar.monthrange(year, month)[1]

    # Generate attendance data for all employees to display
    employees_attendance_data = []
    for employee in employees_to_display:
        attendance_data = generate_dummy_attendance_data(employee, year, month)
        employees_attendance_data.append({
            'employee': employee,
            'attendance_data': attendance_data
        })

    # Calculate navigation dates
    prev_month = month - 1 if month > 1 else 12
    prev_year = year if month > 1 else year - 1
    next_month = month + 1 if month < 12 else 1
    next_year = year if month < 12 else year + 1

    # Create day headers for the table
    day_headers = []
    for day in range(1, days_in_month + 1):
        date = datetime(year, month, day)
        day_headers.append({
            'day': day,
            'is_weekend': date.weekday() == 6,  # Sunday
            'day_name': date.strftime('%a')[:3]  # Mon, Tue, etc.
        })

    context = {
        'page_title': 'Monthly Attendance Sheet',
        'page_subtitle': f'{month_name} {year}' + (f' - {selected_department.dept_name} Department' if selected_department else ''),
        'month': month,
        'year': year,
        'month_name': month_name,
        'days_in_month': days_in_month,
        'day_headers': day_headers,
        'employees_attendance_data': employees_attendance_data,
        'available_departments': available_departments,
        'selected_department': selected_department,
        'user_role': user_role,
        'prev_month': prev_month,
        'prev_year': prev_year,
        'next_month': next_month,
        'next_year': next_year,
        'can_filter_department': user_role == 'HR_ADMINS',
    }

    return render(request, 'attendance_app/attendance_sheet.html', context)

def generate_dummy_attendance_data(employee, year, month):
    """
    Generate dummy attendance data for the specified month.
    """
    attendance_data = {}

    # Get the number of days in the month
    days_in_month = calendar.monthrange(year, month)[1]

    # Define holidays (example dates)
    holidays = [1, 15, 25]  # Example: 1st, 15th, 25th of the month

    for day in range(1, days_in_month + 1):
        date = datetime(year, month, day)
        day_of_week = date.weekday()  # 0=Monday, 6=Sunday

        # Determine attendance status
        if day_of_week == 6:  # Sunday
            status = 'HD'  # Holiday for Sundays
            check_in = None
            check_out = None
        elif day in holidays:
            status = 'HD'  # Public holiday
            check_in = None
            check_out = None
        else:
            # Generate random attendance status
            rand = random.random()
            if rand < 0.85:  # 85% present
                status = 'P'
                # Generate realistic check-in/check-out times
                check_in_hour = random.randint(8, 9)
                check_in_minute = random.randint(0, 59)
                check_out_hour = random.randint(17, 18)
                check_out_minute = random.randint(0, 59)
                check_in = f"{check_in_hour:02d}:{check_in_minute:02d}"
                check_out = f"{check_out_hour:02d}:{check_out_minute:02d}"
            elif rand < 0.92:  # 7% leave
                status = 'LV'
                check_in = None
                check_out = None
            elif rand < 0.97:  # 5% absent
                status = 'A'
                check_in = None
                check_out = None
            else:  # 3% remark/special case
                status = 'RM'
                check_in = "09:30"
                check_out = "15:00"

        attendance_data[day] = {
            'status': status,
            'check_in': check_in,
            'check_out': check_out,
            'date': date,
            'is_weekend': day_of_week == 6,
        }

    return attendance_data
