from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import Http404
from accounts.models import Employee
from orgunit_app.models import Department
from datetime import datetime, timedelta
import calendar
import random
import json

@login_required
def index(request):
    """
    Attendance app index view.
    Displays the main attendance management interface.
    """
    context = {
        'page_title': 'Attendance Management',
        'page_subtitle': 'Track and manage employee attendance',
    }
    return render(request, 'attendance_app/index.html', context)

@login_required
def attendance_sheet_view(request):
    """
    Monthly attendance sheet view with role-based access control.
    """
    # Get month and year from request parameters
    month = int(request.GET.get('month', datetime.now().month))
    year = int(request.GET.get('year', datetime.now().year))
    employee_id = request.GET.get('employee_id')

    # Validate month and year
    if month < 1 or month > 12:
        month = datetime.now().month
    if year < 2020 or year > 2030:
        year = datetime.now().year

    # Determine which employee's sheet to show based on role
    current_user = request.user
    selected_employee = None
    available_employees = []

    # Role-based access control
    if current_user.is_superuser or (hasattr(current_user, 'employee_profile') and
                                   current_user.employee_profile and
                                   current_user.employee_profile.position and
                                   current_user.employee_profile.position.title == 'HR_ADMINS'):
        # HR_ADMINS and Superusers can view all employees
        available_employees = Employee.objects.all().order_by('Fname', 'Lname')
        if employee_id:
            selected_employee = get_object_or_404(Employee, id=employee_id)
        else:
            selected_employee = current_user.employee_profile if hasattr(current_user, 'employee_profile') else available_employees.first()

    elif (hasattr(current_user, 'employee_profile') and
          current_user.employee_profile and
          current_user.employee_profile.position and
          'MANAGER' in current_user.employee_profile.position.title.upper()):
        # Managers can view employees in their department
        user_department = current_user.employee_profile.department
        available_employees = Employee.objects.filter(department=user_department).order_by('Fname', 'Lname')
        if employee_id:
            employee = get_object_or_404(Employee, id=employee_id)
            if employee.department == user_department:
                selected_employee = employee
            else:
                raise Http404("You don't have permission to view this employee's attendance.")
        else:
            selected_employee = current_user.employee_profile

    else:
        # NON_MANAGERIAL employees can only view their own sheet
        if hasattr(current_user, 'employee_profile'):
            selected_employee = current_user.employee_profile
            available_employees = [selected_employee]
        else:
            raise Http404("Employee profile not found.")

    # Generate calendar data
    cal = calendar.monthcalendar(year, month)
    month_name = calendar.month_name[month]

    # Generate dummy attendance data
    attendance_data = generate_dummy_attendance_data(selected_employee, year, month)

    # Serialize attendance data for JavaScript
    attendance_data_json = {}
    for day, data in attendance_data.items():
        attendance_data_json[day] = {
            'status': data['status'],
            'check_in': data['check_in'],
            'check_out': data['check_out'],
            'is_weekend': data['is_weekend'],
        }

    # Calculate navigation dates
    prev_month = month - 1 if month > 1 else 12
    prev_year = year if month > 1 else year - 1
    next_month = month + 1 if month < 12 else 1
    next_year = year if month < 12 else year + 1

    context = {
        'page_title': 'Monthly Attendance Sheet',
        'page_subtitle': f'{month_name} {year} - {selected_employee.Fname} {selected_employee.Lname}' if selected_employee else f'{month_name} {year}',
        'calendar_data': cal,
        'month': month,
        'year': year,
        'month_name': month_name,
        'selected_employee': selected_employee,
        'available_employees': available_employees,
        'attendance_data': attendance_data,
        'attendance_data_json': json.dumps(attendance_data_json),
        'prev_month': prev_month,
        'prev_year': prev_year,
        'next_month': next_month,
        'next_year': next_year,
        'can_select_employee': len(available_employees) > 1,
    }

    return render(request, 'attendance_app/attendance_sheet.html', context)

def generate_dummy_attendance_data(employee, year, month):
    """
    Generate dummy attendance data for the specified month.
    """
    attendance_data = {}

    # Get the number of days in the month
    days_in_month = calendar.monthrange(year, month)[1]

    # Define holidays (example dates)
    holidays = [1, 15, 25]  # Example: 1st, 15th, 25th of the month

    for day in range(1, days_in_month + 1):
        date = datetime(year, month, day)
        day_of_week = date.weekday()  # 0=Monday, 6=Sunday

        # Determine attendance status
        if day_of_week == 6:  # Sunday
            status = 'HD'  # Holiday for Sundays
            check_in = None
            check_out = None
        elif day in holidays:
            status = 'HD'  # Public holiday
            check_in = None
            check_out = None
        else:
            # Generate random attendance status
            rand = random.random()
            if rand < 0.85:  # 85% present
                status = 'P'
                # Generate realistic check-in/check-out times
                check_in_hour = random.randint(8, 9)
                check_in_minute = random.randint(0, 59)
                check_out_hour = random.randint(17, 18)
                check_out_minute = random.randint(0, 59)
                check_in = f"{check_in_hour:02d}:{check_in_minute:02d}"
                check_out = f"{check_out_hour:02d}:{check_out_minute:02d}"
            elif rand < 0.92:  # 7% leave
                status = 'LV'
                check_in = None
                check_out = None
            elif rand < 0.97:  # 5% absent
                status = 'A'
                check_in = None
                check_out = None
            else:  # 3% remark/special case
                status = 'RM'
                check_in = "09:30"
                check_out = "15:00"

        attendance_data[day] = {
            'status': status,
            'check_in': check_in,
            'check_out': check_out,
            'date': date,
            'is_weekend': day_of_week == 6,
        }

    return attendance_data
