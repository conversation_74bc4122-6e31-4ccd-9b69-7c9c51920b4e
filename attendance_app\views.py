from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import Http404
from accounts.models import Employee
from orgunit_app.models import Department
from datetime import datetime, timedelta
import calendar
import random
import json
from .helpers import get_employee_list

@login_required
def index(request):
    """
    Attendance app index view.
    Displays the main attendance management interface.
    """
    context = {
        'page_title': 'Attendance Management',
        'page_subtitle': 'Track and manage employee attendance',
    }
    return render(request, 'attendance_app/index.html', context)

@login_required
def attendance_sheet_view(request):
    """
    Monthly attendance sheet view with table-based layout and role-based access control.
    """
    # Get month, year, and department filter from request parameters
    month = int(request.GET.get('month', datetime.now().month))
    year = int(request.GET.get('year', datetime.now().year))
    department_id = request.GET.get('department_id')

    # Validate month and year
    if month < 1 or month > 12:
        month = datetime.now().month
    if year < 2020 or year > 2030:
        year = datetime.now().year

    # Determine employee access based on role using helper function
    current_user = request.user
    employees_to_display = []
    available_departments = []
    selected_department = None
    user_role = 'NON_MANAGERIAL'  # Default role

    # Check if user has employee profile
    if not (hasattr(current_user, 'employee_profile') and current_user.employee_profile):
        if current_user.is_superuser:
            # Handle superuser without employee profile
            user_role = 'HR_ADMINS'
            from orgunit_app.models import Department
            available_departments = Department.objects.all().order_by('dept_name')

            if department_id:
                selected_department = get_object_or_404(Department, id=department_id)
                employees_to_display = Employee.objects.filter(department=selected_department).order_by('Fname', 'Lname')
            else:
                employees_to_display = Employee.objects.all().order_by('department__dept_name', 'Fname', 'Lname')
        else:
            raise Http404("Employee profile not found.")
    else:
        # Use helper function to get employee list
        try:
            current_employee = current_user.employee_profile
            employees_from_helper = get_employee_list(current_employee.emp_id)
            user_role = current_employee.position.title if current_employee.position else 'NON_MANAGERIAL'

            print(' ->>>>>>>>>>>>>>>>>> USER_ROLE = ', user_role)

            # Handle department filtering for HR_ADMINS and superusers
            if (current_user.is_superuser or user_role == 'HR_ADMINS'):
                user_role = 'HR_ADMINS'
                from orgunit_app.models import Department
                available_departments = Department.objects.all().order_by('dept_name')

                if department_id:
                    selected_department = get_object_or_404(Department, id=department_id)
                    employees_to_display = employees_from_helper.filter(department=selected_department)
                else:
                    employees_to_display = employees_from_helper
            else:
                employees_to_display = employees_from_helper
                if user_role == 'MANAGERS':
                    selected_department = current_employee.department

            # Apply proper ordering based on user role
            if user_role == 'NON_MANAGERIAL':
                # NON_MANAGERIAL users see only themselves
                employees_to_display = employees_to_display.order_by('Fname', 'Lname')
            elif user_role == 'MANAGERS':
                # Managers see themselves first, then other department employees
                current_manager = current_employee
                other_employees = employees_to_display.exclude(emp_id=current_manager.emp_id).order_by('Fname', 'Lname')
                employees_to_display = [current_manager] + list(other_employees)
            else:
                # Other roles: order by department, then name
                employees_to_display = employees_to_display.order_by('department__dept_name', 'Fname', 'Lname')

        except Employee.DoesNotExist:
            raise Http404("Employee profile not found.")
        except Exception as e:
            # Fallback to basic role-based access if helper function fails
            if current_user.is_superuser:
                user_role = 'HR_ADMINS'
                employees_to_display = Employee.objects.all().order_by('department__dept_name', 'Fname', 'Lname')
            else:
                raise Http404(f"Error retrieving employee data: {str(e)}")

    # Generate calendar data
    month_name = calendar.month_name[month]
    days_in_month = calendar.monthrange(year, month)[1]

    # Generate attendance data for all employees to display
    employees_attendance_data = []
    employees_attendance_json = {}

    for employee in employees_to_display:
        attendance_data = generate_dummy_attendance_data(employee, year, month)
        employees_attendance_data.append({
            'employee': employee,
            'attendance_data': attendance_data
        })

        # Serialize attendance data for JavaScript
        employees_attendance_json[employee.id] = {}
        for day, data in attendance_data.items():
            # Only include days with actual status (skip empty Sundays)
            if data['status'] is not None:
                employees_attendance_json[employee.id][day] = {
                    'status': data['status'],
                    'check_in': data['check_in'],
                    'check_out': data['check_out'],
                    'is_weekend': data['is_weekend'],
                }

    # Calculate navigation dates
    prev_month = month - 1 if month > 1 else 12
    prev_year = year if month > 1 else year - 1
    next_month = month + 1 if month < 12 else 1
    next_year = year if month < 12 else year + 1

    # Create day headers for the table
    today = datetime.now().date()
    day_headers = []
    for day in range(1, days_in_month + 1):
        date = datetime(year, month, day)
        is_today = (date.date() == today)
        day_headers.append({
            'day': day,
            'is_weekend': date.weekday() == 6,  # Sunday
            'is_today': is_today,
            'day_name': date.strftime('%a')[:3]  # Mon, Tue, etc.
        })

    # Create subtitle based on user role and context
    subtitle_parts = [f'{month_name} {year}']
    if selected_department:
        dept_name = getattr(selected_department, 'dept_name', getattr(selected_department, 'name', str(selected_department)))
        subtitle_parts.append(f'{dept_name} Department')
    elif user_role == 'HR_ADMINS':
        subtitle_parts.append('All Departments')

    context = {
        'page_title': 'Monthly Attendance Sheet',
        'page_subtitle': ' - '.join(subtitle_parts),
        'month': month,
        'year': year,
        'month_name': month_name,
        'days_in_month': days_in_month,
        'day_headers': day_headers,
        'employees_attendance_data': employees_attendance_data,
        'employees_attendance_json': json.dumps(employees_attendance_json),
        'available_departments': available_departments,
        'selected_department': selected_department,
        'user_role': user_role,
        'prev_month': prev_month,
        'prev_year': prev_year,
        'next_month': next_month,
        'next_year': next_year,
        'can_filter_department': user_role == 'HR_ADMINS',
        'employee_count': len(employees_attendance_data),
    }

    return render(request, 'attendance_app/attendance_sheet.html', context)

def generate_dummy_attendance_data(employee, year, month):
    """
    Generate dummy attendance data for the specified month.
    Note: employee parameter is for future use when implementing employee-specific patterns.
    """
    attendance_data = {}

    # Get the number of days in the month
    days_in_month = calendar.monthrange(year, month)[1]

    # Define holidays (example dates) - specific public holidays only
    holidays = [1, 15, 25]  # Example: 1st, 15th, 25th of the month

    for day in range(1, days_in_month + 1):
        date = datetime(year, month, day)
        day_of_week = date.weekday()  # 0=Monday, 6=Sunday

        # Determine attendance status
        if day_of_week == 6:  # Sunday
            # Sundays are non-working days - leave empty (no status)
            attendance_data[day] = {
                'status': None,
                'check_in': None,
                'check_out': None,
                'date': date,
                'is_weekend': True,
            }
            continue
        elif day in holidays:
            status = 'HD'  # Public holiday
            check_in = None
            check_out = None
        else:
            # Generate random attendance status for working days
            rand = random.random()
            if rand < 0.85:  # 85% present
                status = 'P'
                # Generate realistic check-in/check-out times
                check_in_hour = random.randint(8, 9)
                check_in_minute = random.randint(0, 59)
                check_out_hour = random.randint(17, 18)
                check_out_minute = random.randint(0, 59)
                check_in = f"{check_in_hour:02d}:{check_in_minute:02d}"
                check_out = f"{check_out_hour:02d}:{check_out_minute:02d}"
            elif rand < 0.92:  # 7% leave
                status = 'LV'
                check_in = None
                check_out = None
            elif rand < 0.97:  # 5% absent
                status = 'A'
                check_in = None
                check_out = None
            else:  # 3% remark/special case
                status = 'RM'
                check_in = "09:30"
                check_out = "15:00"

        attendance_data[day] = {
            'status': status,
            'check_in': check_in,
            'check_out': check_out,
            'date': date,
            'is_weekend': day_of_week == 6,
        }

    return attendance_data
