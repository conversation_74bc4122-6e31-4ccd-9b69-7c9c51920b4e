{% extends 'base.html' %}

{% block title %}Dashboard - EEU Attendance System{% endblock %}

{% block page_title %}Dashboard{% endblock %}
{% block page_subtitle %}Overview of your attendance and leave management{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-xl font-semibold text-gray-900 mb-2">
                    Welcome back{% if employee %}, {{ employee.Fname }}{% endif %}!
                </h2>
                <p class="text-gray-600">
                    Today is {{ current_date|date:"l, F j, Y" }} - {{ current_time|time:"g:i A" }}
                </p>
                {% if employee %}
                    <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                        {% if user_department %}
                            <span class="flex items-center">
                                <i class="fas fa-building mr-1 text-emerald-500"></i>
                                {{ user_department.name }}
                            </span>
                        {% endif %}
                        {% if user_position %}
                            <span class="flex items-center">
                                <i class="fas fa-briefcase mr-1 text-emerald-500"></i>
                                {{ user_position.name }}
                            </span>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
            <div class="text-right">
                <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-2xl text-emerald-600"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Employees -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-xl text-emerald-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Employees</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_employees }}</p>
                </div>
            </div>
        </div>

        <!-- Total Departments -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-building text-xl text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Departments</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_departments }}</p>
                </div>
            </div>
        </div>

        <!-- Total Positions -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-briefcase text-xl text-purple-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Positions</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_positions }}</p>
                </div>
            </div>
        </div>

        <!-- Attendance Status -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-xl text-green-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Today's Status</p>
                    <p class="text-lg font-bold text-green-600">Ready</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Department Statistics -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Department Overview</h3>
                    <p class="text-sm text-gray-600">Employee distribution across departments</p>
                </div>
                <div class="p-6">
                    {% if department_stats %}
                        <div class="space-y-4">
                            {% for dept in department_stats %}
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-emerald-500 rounded-full mr-3"></div>
                                        <span class="text-sm font-medium text-gray-900">{{ dept.dept_name }}</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-gray-500">{{ dept.employee_count }} employees</span>
                                        <div class="w-20 bg-gray-200 rounded-full h-2">
                                            <div class="bg-pink-500 h-2 rounded-full" style="width: {% widthratio dept.employee_count total_employees 100 %}%;"></div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-8">
                            <i class="fas fa-building text-4xl text-gray-300 mb-4"></i>
                            <p class="text-gray-500">No department data available</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Activities</h3>
                    <p class="text-sm text-gray-600">Latest system updates</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        {% for activity in recent_activities %}
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    {% if activity.type == 'attendance' %}
                                        <div class="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-clock text-sm text-emerald-600"></i>
                                        </div>
                                    {% elif activity.type == 'leave' %}
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-calendar-alt text-sm text-blue-600"></i>
                                        </div>
                                    {% else %}
                                        <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-info text-sm text-gray-600"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-gray-900">{{ activity.message }}</p>
                                    <p class="text-xs text-gray-500 mt-1">Just now</p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
            <p class="text-sm text-gray-600">Common tasks and shortcuts</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button class="flex items-center justify-center p-4 border-2 border-dashed border-emerald-300 rounded-lg hover:border-emerald-500 hover:bg-emerald-50 transition-colors duration-200 group">
                    <div class="text-center">
                        <i class="fas fa-clock text-2xl text-emerald-500 mb-2 group-hover:text-emerald-600"></i>
                        <p class="text-sm font-medium text-gray-700 group-hover:text-emerald-700">Mark Attendance</p>
                    </div>
                </button>
                
                <button class="flex items-center justify-center p-4 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors duration-200 group">
                    <div class="text-center">
                        <i class="fas fa-calendar-plus text-2xl text-blue-500 mb-2 group-hover:text-blue-600"></i>
                        <p class="text-sm font-medium text-gray-700 group-hover:text-blue-700">Request Leave</p>
                    </div>
                </button>
                
                <button class="flex items-center justify-center p-4 border-2 border-dashed border-purple-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors duration-200 group">
                    <div class="text-center">
                        <i class="fas fa-chart-line text-2xl text-purple-500 mb-2 group-hover:text-purple-600"></i>
                        <p class="text-sm font-medium text-gray-700 group-hover:text-purple-700">View Reports</p>
                    </div>
                </button>
                
                <button class="flex items-center justify-center p-4 border-2 border-dashed border-orange-300 rounded-lg hover:border-orange-500 hover:bg-orange-50 transition-colors duration-200 group">
                    <div class="text-center">
                        <i class="fas fa-user-plus text-2xl text-orange-500 mb-2 group-hover:text-orange-600"></i>
                        <p class="text-sm font-medium text-gray-700 group-hover:text-orange-700">Add Employee</p>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
