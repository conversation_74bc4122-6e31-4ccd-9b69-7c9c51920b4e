{% extends 'base.html' %}

{% block title %}My Dashboard - EEU System{% endblock %}

{% block page_title %}My Dashboard{% endblock %}
{% block page_subtitle %}Welcome back{% if employee %}, {{ employee.Fname }}{% endif %}!{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Personal Info Section -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-xl font-semibold text-gray-900 mb-2">
                    <i class="fas fa-user text-emerald-500 mr-2"></i>
                    Personal Information
                </h2>
                {% if employee %}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                            <p class="text-sm text-gray-500">Full Name</p>
                            <p class="font-medium text-gray-900">{{ employee.Fname }} {{ employee.Lname }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Employee ID</p>
                            <p class="font-medium text-gray-900">{{ employee_id|default:"N/A" }}</p>
                        </div>
                        {% if user_department %}
                            <div>
                                <p class="text-sm text-gray-500">Department</p>
                                <p class="font-medium text-gray-900">{{ user_department.name }}</p>
                            </div>
                        {% endif %}
                        {% if user_position %}
                            <div>
                                <p class="text-sm text-gray-500">Position</p>
                                <p class="font-medium text-gray-900">{{ user_position.name }}</p>
                            </div>
                        {% endif %}
                    </div>
                {% else %}
                    <p class="text-gray-600">Employee profile not found. Please contact HR.</p>
                {% endif %}
            </div>
            <div class="text-right">
                <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-2xl text-emerald-600"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Attendance & Leave Balance -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Today's Attendance -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-clock text-emerald-500 mr-2"></i>
                Today's Attendance
            </h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">Status</span>
                    <span class="px-3 py-1 rounded-full text-xs font-medium {% if attendance_status.checked_in %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {{ attendance_status.status_message }}
                    </span>
                </div>
                {% if attendance_status.check_in_time %}
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Check-in Time</span>
                        <span class="font-medium text-gray-900">{{ attendance_status.check_in_time }}</span>
                    </div>
                {% endif %}
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">Work Hours Today</span>
                    <span class="font-medium text-gray-900">{{ attendance_status.work_hours_today }}</span>
                </div>
                <div class="pt-4">
                    {% if not attendance_status.checked_in %}
                        <button class="w-full bg-emerald-500 text-white py-2 px-4 rounded-lg hover:bg-emerald-600 transition-colors">
                            <i class="fas fa-clock mr-2"></i>
                            Check In
                        </button>
                    {% else %}
                        <button class="w-full bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600 transition-colors">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Check Out
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Leave Balance Summary -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-calendar-alt text-emerald-500 mr-2"></i>
                Leave Balance
            </h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">Annual Leave</span>
                    <span class="font-medium text-gray-900">{{ leave_balance.annual_leave }} days</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">Sick Leave</span>
                    <span class="font-medium text-gray-900">{{ leave_balance.sick_leave }} days</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">Personal Leave</span>
                    <span class="font-medium text-gray-900">{{ leave_balance.personal_leave }} days</span>
                </div>
                <div class="border-t pt-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-900">Total Available</span>
                        <span class="font-bold text-emerald-600">{{ leave_balance.total_available }} days</span>
                    </div>
                </div>
                <div class="pt-2">
                    <button class="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-calendar-plus mr-2"></i>
                        Request Leave
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity & Upcoming Events -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
                <p class="text-sm text-gray-600">Your latest attendance and leave records</p>
            </div>
            <div class="p-6">
                {% if recent_activities %}
                    <div class="space-y-4">
                        {% for activity in recent_activities %}
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    {% if activity.type == 'attendance' %}
                                        <div class="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-clock text-sm text-emerald-600"></i>
                                        </div>
                                    {% elif activity.type == 'leave' %}
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-calendar-alt text-sm text-blue-600"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-gray-900">{{ activity.message }}</p>
                                    <p class="text-xs text-gray-500 mt-1">{{ activity.date|date:"M j, Y" }}</p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <i class="fas fa-history text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No recent activity</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Upcoming Events -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Upcoming Events</h3>
                <p class="text-sm text-gray-600">Your scheduled leave and important dates</p>
            </div>
            <div class="p-6">
                {% if upcoming_events %}
                    <div class="space-y-4">
                        {% for event in upcoming_events %}
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-calendar text-sm text-purple-600"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-gray-900">{{ event.message }}</p>
                                    <p class="text-xs text-gray-500 mt-1">{{ event.date|date:"M j, Y" }}</p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <i class="fas fa-calendar text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No upcoming events</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
            <p class="text-sm text-gray-600">Common tasks and shortcuts</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="{% url 'attendance_app:index' %}" class="flex items-center justify-center p-4 border-2 border-dashed border-emerald-300 rounded-lg hover:border-emerald-500 hover:bg-emerald-50 transition-colors duration-200 group">
                    <div class="text-center">
                        <i class="fas fa-clock text-2xl text-emerald-500 mb-2 group-hover:text-emerald-600"></i>
                        <p class="text-sm font-medium text-gray-700 group-hover:text-emerald-700">View Attendance</p>
                    </div>
                </a>
                
                <a href="{% url 'leave_app:index' %}" class="flex items-center justify-center p-4 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors duration-200 group">
                    <div class="text-center">
                        <i class="fas fa-calendar-plus text-2xl text-blue-500 mb-2 group-hover:text-blue-600"></i>
                        <p class="text-sm font-medium text-gray-700 group-hover:text-blue-700">Request Leave</p>
                    </div>
                </a>
                
                <button class="flex items-center justify-center p-4 border-2 border-dashed border-purple-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors duration-200 group">
                    <div class="text-center">
                        <i class="fas fa-user-edit text-2xl text-purple-500 mb-2 group-hover:text-purple-600"></i>
                        <p class="text-sm font-medium text-gray-700 group-hover:text-purple-700">Update Profile</p>
                    </div>
                </button>
                
                <button class="flex items-center justify-center p-4 border-2 border-dashed border-orange-300 rounded-lg hover:border-orange-500 hover:bg-orange-50 transition-colors duration-200 group">
                    <div class="text-center">
                        <i class="fas fa-question-circle text-2xl text-orange-500 mb-2 group-hover:text-orange-600"></i>
                        <p class="text-sm font-medium text-gray-700 group-hover:text-orange-700">Get Help</p>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
