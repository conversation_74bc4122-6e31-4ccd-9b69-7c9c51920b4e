from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from accounts.models import Employee
from orgunit_app.models import Department, Position
from django.db.models import Count
from datetime import datetime, timedelta

@login_required
def dashboard_view(request):
    """
    Main dashboard view with role-based routing.
    Redirects NON_MANAGERIAL users to employee dashboard.
    """
    # Check if user should see employee dashboard
    if (request.user.is_authenticated and
        hasattr(request.user, 'employee_profile') and
        request.user.employee_profile and
        hasattr(request.user.employee_profile, 'position') and
        request.user.employee_profile.position and
        request.user.employee_profile.position.name == 'NON_MANAGERIAL'):
        return redirect('dashboard:employee_dashboard')

    # Admin dashboard for HR_ADMINS, superusers, admin group, or users without profiles
    context = {}

    # Get current user's employee profile if exists
    try:
        employee = request.user.employee_profile
        context['employee'] = employee
        context['user_department'] = employee.department
        context['user_position'] = employee.position
    except:
        context['employee'] = None

    # Dashboard statistics
    total_employees = Employee.objects.count()
    context['total_employees'] = total_employees
    context['total_departments'] = Department.objects.count()
    context['total_positions'] = Position.objects.count()

    # Department-wise employee count
    department_stats = Department.objects.annotate(
        employee_count=Count('employees_in_department')
    ).order_by('-employee_count')[:5]
    context['department_stats'] = department_stats

    # Recent activity (placeholder for future implementation)
    context['recent_activities'] = [
        {'type': 'attendance', 'message': 'Attendance system ready for implementation'},
        {'type': 'leave', 'message': 'Leave management system ready for implementation'},
        {'type': 'system', 'message': 'Dashboard successfully configured'},
    ]

    # Current date and time
    context['current_date'] = datetime.now().date()
    context['current_time'] = datetime.now().time()

    return render(request, 'dashboard/dashboard.html', context)

@login_required
def employee_dashboard_view(request):
    """
    Simplified dashboard view for NON_MANAGERIAL employees.
    Displays essential at-a-glance information.
    """
    context = {}

    # Get current user's employee profile
    try:
        employee = request.user.employee_profile
        context['employee'] = employee
        context['user_department'] = employee.department
        context['user_position'] = employee.position
        context['employee_id'] = employee.id
    except:
        context['employee'] = None

    # Today's attendance status (placeholder for future implementation)
    context['attendance_status'] = {
        'checked_in': False,  # Will be replaced with actual logic
        'check_in_time': None,
        'work_hours_today': '0:00',
        'status_message': 'Not checked in today'
    }

    # Leave balance summary (placeholder for future implementation)
    context['leave_balance'] = {
        'annual_leave': 15,
        'sick_leave': 10,
        'personal_leave': 5,
        'total_available': 30
    }

    # Recent activity (last 5 records - placeholder)
    context['recent_activities'] = [
        {
            'type': 'attendance',
            'date': (datetime.now() - timedelta(days=1)).date(),
            'message': 'Checked in at 9:00 AM',
            'status': 'completed'
        },
        {
            'type': 'attendance',
            'date': (datetime.now() - timedelta(days=2)).date(),
            'message': 'Checked in at 8:45 AM',
            'status': 'completed'
        },
        {
            'type': 'leave',
            'date': (datetime.now() - timedelta(days=5)).date(),
            'message': 'Annual leave request approved',
            'status': 'approved'
        }
    ]

    # Upcoming events (placeholder)
    context['upcoming_events'] = [
        {
            'type': 'leave',
            'date': (datetime.now() + timedelta(days=10)).date(),
            'message': 'Annual leave: Dec 25-26',
            'status': 'approved'
        }
    ]

    # Current date and time
    context['current_date'] = datetime.now().date()
    context['current_time'] = datetime.now().time()

    return render(request, 'dashboard/employee_dashboard.html', context)
