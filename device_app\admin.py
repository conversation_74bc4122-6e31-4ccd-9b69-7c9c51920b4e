from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from datetime import timedelta
from .models import Device, AttendanceRecordLog, DeviceStatus

# Custom admin actions
def mark_devices_active(modeladmin, request, queryset):
    """Mark selected devices as active"""
    updated = queryset.update(status='active')
    modeladmin.message_user(request, f'{updated} devices marked as active.')
mark_devices_active.short_description = "Mark selected devices as active"

def mark_devices_inactive(modeladmin, request, queryset):
    """Mark selected devices as inactive"""
    updated = queryset.update(status='inactive')
    modeladmin.message_user(request, f'{updated} devices marked as inactive.')
mark_devices_inactive.short_description = "Mark selected devices as inactive"

def mark_devices_maintenance(modeladmin, request, queryset):
    """Mark selected devices as under maintenance"""
    updated = queryset.update(status='maintenance')
    modeladmin.message_user(request, f'{updated} devices marked as under maintenance.')
mark_devices_maintenance.short_description = "Mark selected devices as under maintenance"

# Inline admin classes
class AttendanceRecordLogInline(admin.TabularInline):
    model = AttendanceRecordLog
    extra = 0
    readonly_fields = ['timestamp', 'sync_status']
    fields = ['employee_id', 'timestamp', 'status', 'sync_status']
    ordering = ['-timestamp']

    def has_add_permission(self, request, obj=None):
        return False  # Prevent adding records through device admin

class DeviceStatusInline(admin.TabularInline):
    model = DeviceStatus
    extra = 0
    readonly_fields = ['last_attempt']
    fields = ['date', 'success', 'last_attempt']
    ordering = ['-date']

@admin.register(Device)
class DeviceAdmin(admin.ModelAdmin):
    list_display = [
        'display_name',
        'location',
        'serial_num',
        'IP',
        'status_badge',
        'last_successful_pull_display',
        'recent_records_count',
        'connection_status'
    ]
    list_filter = [
        'status',
        'location',
        'last_successful_pull'
    ]
    search_fields = [
        'display_name',
        'location',
        'serial_num',
        'IP'
    ]
    readonly_fields = [
        'last_successful_pull',
        'device_statistics',
        'recent_activity'
    ]
    fieldsets = (
        ('Basic Information', {
            'fields': ('display_name', 'location', 'serial_num')
        }),
        ('Network Configuration', {
            'fields': ('IP', 'pwd'),
            'classes': ('collapse',)
        }),
        ('Status & Monitoring', {
            'fields': ('status', 'last_successful_pull')
        }),
        ('Statistics', {
            'fields': ('device_statistics', 'recent_activity'),
            'classes': ('collapse',)
        }),
    )
    actions = [mark_devices_active, mark_devices_inactive, mark_devices_maintenance]
    inlines = [DeviceStatusInline, AttendanceRecordLogInline]

    def status_badge(self, obj):
        """Display status with colored badge"""
        colors = {
            'active': '#28a745',
            'inactive': '#dc3545',
            'maintenance': '#ffc107'
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; border-radius: 3px; font-size: 11px; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_badge.short_description = 'Status'

    def last_successful_pull_display(self, obj):
        """Display last successful pull with time formatting"""
        if obj.last_successful_pull:
            now = timezone.now()
            diff = now - obj.last_successful_pull

            if diff.days > 7:
                return format_html(
                    '<span style="color: #dc3545; font-weight: bold;">{}</span>',
                    obj.last_successful_pull.strftime('%Y-%m-%d %H:%M')
                )
            elif diff.days > 1:
                return format_html(
                    '<span style="color: #ffc107; font-weight: bold;">{}</span>',
                    obj.last_successful_pull.strftime('%Y-%m-%d %H:%M')
                )
            else:
                return format_html(
                    '<span style="color: #28a745;">{}</span>',
                    obj.last_successful_pull.strftime('%Y-%m-%d %H:%M')
                )
        return format_html('<span style="color: #dc3545;">Never</span>')
    last_successful_pull_display.short_description = 'Last Pull'

    def recent_records_count(self, obj):
        """Count of records in last 24 hours"""
        yesterday = timezone.now() - timedelta(days=1)
        count = AttendanceRecordLog.objects.filter(
            device=obj,
            timestamp__gte=yesterday
        ).count()

        if count > 50:
            color = '#28a745'
        elif count > 10:
            color = '#ffc107'
        else:
            color = '#dc3545'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            count
        )
    recent_records_count.short_description = '24h Records'

    def connection_status(self, obj):
        """Display connection status based on last pull"""
        if not obj.last_successful_pull:
            return format_html(
                '<span style="color: #dc3545;">●</span> Never Connected'
            )

        now = timezone.now()
        diff = now - obj.last_successful_pull

        if diff.total_seconds() < 3600:  # Less than 1 hour
            return format_html(
                '<span style="color: #28a745;">●</span> Online'
            )
        elif diff.total_seconds() < 86400:  # Less than 24 hours
            return format_html(
                '<span style="color: #ffc107;">●</span> Recently Active'
            )
        else:
            return format_html(
                '<span style="color: #dc3545;">●</span> Offline'
            )
    connection_status.short_description = 'Connection'

    def device_statistics(self, obj):
        """Display device statistics"""
        total_records = AttendanceRecordLog.objects.filter(device=obj).count()
        today_records = AttendanceRecordLog.objects.filter(
            device=obj,
            timestamp__date=timezone.now().date()
        ).count()
        week_records = AttendanceRecordLog.objects.filter(
            device=obj,
            timestamp__gte=timezone.now() - timedelta(days=7)
        ).count()

        return format_html(
            '<div style="line-height: 1.5;">'
            '<strong>Total Records:</strong> {}<br>'
            '<strong>Today:</strong> {}<br>'
            '<strong>This Week:</strong> {}<br>'
            '</div>',
            total_records,
            today_records,
            week_records
        )
    device_statistics.short_description = 'Statistics'

    def recent_activity(self, obj):
        """Display recent activity"""
        recent_logs = AttendanceRecordLog.objects.filter(
            device=obj
        ).order_by('-timestamp')[:5]

        if not recent_logs:
            return "No recent activity"

        activity_html = '<div style="line-height: 1.4;">'
        for log in recent_logs:
            activity_html += f'<div style="margin-bottom: 3px;">'
            activity_html += f'<strong>{log.employee_id}</strong> - {log.get_status_display()} '
            activity_html += f'<small>({log.timestamp.strftime("%m/%d %H:%M")})</small>'
            activity_html += '</div>'
        activity_html += '</div>'

        return format_html(activity_html)
    recent_activity.short_description = 'Recent Activity'

    def changelist_view(self, request, extra_context=None):
        """Add custom statistics to the changelist view"""
        extra_context = extra_context or {}

        # Calculate device statistics
        total_devices = Device.objects.count()
        active_devices = Device.objects.filter(status='active').count()
        inactive_devices = Device.objects.filter(status='inactive').count()
        maintenance_devices = Device.objects.filter(status='maintenance').count()

        # Calculate today's records
        today = timezone.now().date()
        total_records_today = AttendanceRecordLog.objects.filter(
            timestamp__date=today
        ).count()

        # Calculate online devices (last pull within 1 hour)
        one_hour_ago = timezone.now() - timedelta(hours=1)
        online_devices = Device.objects.filter(
            last_successful_pull__gte=one_hour_ago
        ).count()

        extra_context.update({
            'total_devices': total_devices,
            'active_devices_count': active_devices,
            'inactive_devices_count': inactive_devices,
            'maintenance_devices_count': maintenance_devices,
            'total_records_today': total_records_today,
            'online_devices_count': online_devices,
        })

        return super().changelist_view(request, extra_context=extra_context)

@admin.register(AttendanceRecordLog)
class AttendanceRecordLogAdmin(admin.ModelAdmin):
    list_display = [
        'device',
        'employee_id',
        'timestamp',
        'status_badge',
        'sync_status_badge'
    ]
    list_filter = [
        'status',
        'sync_status',
        'device',
        'timestamp'
    ]
    search_fields = [
        'employee_id',
        'device__display_name',
        'device__location'
    ]
    readonly_fields = ['timestamp']
    date_hierarchy = 'timestamp'

    def status_badge(self, obj):
        """Display status with colored badge"""
        colors = {
            'IN': '#28a745',
            'OUT': '#dc3545'
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_badge.short_description = 'Status'

    def sync_status_badge(self, obj):
        """Display sync status with badge"""
        if obj.sync_status:
            return format_html(
                '<span style="background-color: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">✓ Synced</span>'
            )
        else:
            return format_html(
                '<span style="background-color: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">✗ Pending</span>'
            )
    sync_status_badge.short_description = 'Sync Status'

@admin.register(DeviceStatus)
class DeviceStatusAdmin(admin.ModelAdmin):
    list_display = [
        'device',
        'date',
        'success_badge',
        'last_attempt'
    ]
    list_filter = [
        'success',
        'date',
        'device'
    ]
    search_fields = [
        'device__display_name',
        'device__location'
    ]
    readonly_fields = ['last_attempt']
    date_hierarchy = 'date'

    def success_badge(self, obj):
        """Display success status with badge"""
        if obj.success:
            return format_html(
                '<span style="background-color: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">✓ Success</span>'
            )
        else:
            return format_html(
                '<span style="background-color: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">✗ Failed</span>'
            )
    success_badge.short_description = 'Status'

# Customize admin site headers
admin.site.site_header = "EEU Device Management System"
admin.site.site_title = "EEU Device Admin"
admin.site.index_title = "Device Management Dashboard"
