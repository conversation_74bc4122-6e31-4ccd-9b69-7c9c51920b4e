from django.apps import AppConfig
import logging

logger = logging.getLogger('device_management')

class DeviceAppConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'device_app'
    verbose_name = 'Device Management'

    def ready(self):
        """
        Initialize the device app when Django starts.
        This is called once Django has loaded all models.
        """
        # Import here to avoid circular imports
        try:
            from .scheduler import start_device_scheduler

            # Auto-start the scheduler when the app is ready
            # This can be disabled by setting DEVICE_AUTO_START_SCHEDULER = False in settings
            from django.conf import settings

            auto_start = getattr(settings, 'DEVICE_AUTO_START_SCHEDULER', False)

            if auto_start:
                logger.info("Auto-starting device scheduler...")
                success = start_device_scheduler()
                if success:
                    logger.info("Device scheduler auto-started successfully")
                else:
                    logger.error("Failed to auto-start device scheduler")
            else:
                logger.info("Device scheduler auto-start is disabled")

        except Exception as e:
            logger.error(f"Error during device app initialization: {e}")
