"""
Background tasks for device management system.
Handles device pinging, data retrieval, and status updates.
"""

import socket
import logging
import random
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from django.utils import timezone
from django.db import transaction
from django.conf import settings
from .models import Device, AttendanceRecordLog, DeviceStatus

# Configure logging
logger = logging.getLogger('device_management')

class DeviceConnectionError(Exception):
    """Custom exception for device connection issues"""
    pass

class DataRetrievalError(Exception):
    """Custom exception for data retrieval issues"""
    pass

def ping_device(device: Device, timeout: int = 5) -> bool:
    """
    Ping a device by attempting to connect to its IP address on port 9085.
    
    Args:
        device: Device instance to ping
        timeout: Connection timeout in seconds
        
    Returns:
        bool: True if device is reachable, False otherwise
    """
    try:
        logger.info(f"Pinging device {device.display_name} at {device.IP}:9085")
        
        # Create socket connection to test device availability
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        
        result = sock.connect_ex((device.IP, 9085))
        sock.close()
        
        if result == 0:
            logger.info(f"Device {device.display_name} is online")
            return True
        else:
            logger.warning(f"Device {device.display_name} is offline (connection failed)")
            return False
            
    except socket.gaierror as e:
        logger.error(f"DNS resolution failed for device {device.display_name}: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error pinging device {device.display_name}: {e}")
        return False

def simulate_attendance_data_retrieval(device: Device) -> List[Dict[str, Any]]:
    """
    Simulate attendance data retrieval from device.
    In production, this would connect to the actual device API.
    
    Args:
        device: Device instance to retrieve data from
        
    Returns:
        List of attendance records as dictionaries
    """
    # Simulate realistic attendance data
    sample_records = []
    
    # Generate 0-5 random records for the last 24 hours
    num_records = random.randint(0, 5)
    
    for i in range(num_records):
        # Random timestamp within last 24 hours
        hours_ago = random.randint(1, 24)
        timestamp = timezone.now() - timedelta(hours=hours_ago)
        
        record = {
            'employee_id': f'EMP{random.randint(1001, 1050):04d}',
            'timestamp': timestamp,
            'status': random.choice(['IN', 'OUT']),
            'device_id': device.id
        }
        sample_records.append(record)
    
    return sample_records

def retrieve_attendance_data(device: Device, max_retries: int = 3) -> List[Dict[str, Any]]:
    """
    Retrieve attendance data from a device.
    
    Args:
        device: Device instance to retrieve data from
        max_retries: Maximum number of retry attempts
        
    Returns:
        List of attendance records
        
    Raises:
        DataRetrievalError: If data retrieval fails after all retries
    """
    logger.info(f"Retrieving attendance data from device {device.display_name}")
    
    for attempt in range(max_retries):
        try:
            # In production, this would make actual API calls to the device
            # For now, we'll simulate the data retrieval
            
            # Simulate potential connection issues
            if random.random() < 0.1:  # 10% chance of failure
                raise ConnectionError("Simulated connection timeout")
            
            # Retrieve simulated data
            records = simulate_attendance_data_retrieval(device)
            
            logger.info(f"Successfully retrieved {len(records)} records from device {device.display_name}")
            return records
            
        except Exception as e:
            logger.warning(f"Attempt {attempt + 1} failed for device {device.display_name}: {e}")
            
            if attempt == max_retries - 1:
                raise DataRetrievalError(f"Failed to retrieve data after {max_retries} attempts: {e}")
            
            # Wait before retry (exponential backoff)
            import time
            time.sleep(2 ** attempt)
    
    return []

def process_device(device: Device) -> Dict[str, Any]:
    """
    Process a single device: ping, retrieve data, and update status.
    
    Args:
        device: Device instance to process
        
    Returns:
        Dictionary with processing results
    """
    result = {
        'device_id': device.id,
        'device_name': device.display_name,
        'ping_success': False,
        'data_retrieval_success': False,
        'records_retrieved': 0,
        'errors': []
    }
    
    try:
        # Step 1: Ping the device
        ping_success = ping_device(device)
        result['ping_success'] = ping_success
        
        if ping_success:
            # Update device status to active and set last successful pull
            with transaction.atomic():
                device.status = 'active'
                device.last_successful_pull = timezone.now()
                device.save()
            
            logger.info(f"Device {device.display_name} marked as active")
            
            # Step 2: Retrieve attendance data
            try:
                attendance_records = retrieve_attendance_data(device)
                result['records_retrieved'] = len(attendance_records)
                
                # Step 3: Save attendance records
                with transaction.atomic():
                    for record_data in attendance_records:
                        # Check if record already exists to avoid duplicates
                        existing_record = AttendanceRecordLog.objects.filter(
                            device=device,
                            employee_id=record_data['employee_id'],
                            timestamp=record_data['timestamp']
                        ).first()
                        
                        if not existing_record:
                            AttendanceRecordLog.objects.create(
                                device=device,
                                employee_id=record_data['employee_id'],
                                timestamp=record_data['timestamp'],
                                status=record_data['status'],
                                sync_status=True  # Mark as synced since we just retrieved it
                            )
                
                # Update device status for today
                today = timezone.now().date()
                device_status, created = DeviceStatus.objects.get_or_create(
                    device=device,
                    date=today,
                    defaults={'success': True, 'last_attempt': timezone.now()}
                )
                
                if not created:
                    device_status.success = True
                    device_status.last_attempt = timezone.now()
                    device_status.save()
                
                result['data_retrieval_success'] = True
                logger.info(f"Successfully processed device {device.display_name}: {len(attendance_records)} records")
                
            except DataRetrievalError as e:
                error_msg = f"Data retrieval failed for device {device.display_name}: {e}"
                logger.error(error_msg)
                result['errors'].append(error_msg)
                
                # Update device status to indicate failure
                today = timezone.now().date()
                device_status, created = DeviceStatus.objects.get_or_create(
                    device=device,
                    date=today,
                    defaults={'success': False, 'last_attempt': timezone.now()}
                )
                
                if not created:
                    device_status.success = False
                    device_status.last_attempt = timezone.now()
                    device_status.save()
        
        else:
            # Ping failed - mark device as inactive
            with transaction.atomic():
                device.status = 'inactive'
                device.save()
            
            error_msg = f"Device {device.display_name} is offline"
            logger.warning(error_msg)
            result['errors'].append(error_msg)
            
            # Update device status to indicate failure
            today = timezone.now().date()
            device_status, created = DeviceStatus.objects.get_or_create(
                device=device,
                date=today,
                defaults={'success': False, 'last_attempt': timezone.now()}
            )
            
            if not created:
                device_status.success = False
                device_status.last_attempt = timezone.now()
                device_status.save()
    
    except Exception as e:
        error_msg = f"Unexpected error processing device {device.display_name}: {e}"
        logger.error(error_msg)
        result['errors'].append(error_msg)
    
    return result

def process_all_devices() -> Dict[str, Any]:
    """
    Process all devices in the database.

    Returns:
        Dictionary with overall processing results
    """
    logger.info("Starting device processing workflow for all devices")

    start_time = timezone.now()

    # Get all devices from database
    devices = Device.objects.all()

    if not devices.exists():
        logger.warning("No devices found in database")
        return {
            'status': 'completed',
            'start_time': start_time,
            'end_time': timezone.now(),
            'total_devices': 0,
            'successful_pings': 0,
            'successful_data_retrievals': 0,
            'total_records_retrieved': 0,
            'errors': ['No devices found in database']
        }

    results = {
        'status': 'running',
        'start_time': start_time,
        'total_devices': devices.count(),
        'successful_pings': 0,
        'successful_data_retrievals': 0,
        'total_records_retrieved': 0,
        'device_results': [],
        'errors': []
    }

    logger.info(f"Processing {devices.count()} devices")

    # Process each device
    for device in devices:
        try:
            device_result = process_device(device)
            results['device_results'].append(device_result)

            # Update counters
            if device_result['ping_success']:
                results['successful_pings'] += 1

            if device_result['data_retrieval_success']:
                results['successful_data_retrievals'] += 1

            results['total_records_retrieved'] += device_result['records_retrieved']

            # Collect errors
            if device_result['errors']:
                results['errors'].extend(device_result['errors'])

        except Exception as e:
            error_msg = f"Critical error processing device {device.display_name}: {e}"
            logger.error(error_msg)
            results['errors'].append(error_msg)

    # Finalize results
    end_time = timezone.now()
    results['end_time'] = end_time
    results['duration'] = (end_time - start_time).total_seconds()
    results['status'] = 'completed'

    # Log summary
    logger.info(
        f"Device processing completed. "
        f"Processed: {results['total_devices']}, "
        f"Successful pings: {results['successful_pings']}, "
        f"Successful data retrievals: {results['successful_data_retrievals']}, "
        f"Total records: {results['total_records_retrieved']}, "
        f"Duration: {results['duration']:.2f}s"
    )

    if results['errors']:
        logger.warning(f"Processing completed with {len(results['errors'])} errors")

    return results

def get_device_processing_summary() -> Dict[str, Any]:
    """
    Get a summary of recent device processing activity.

    Returns:
        Dictionary with processing summary
    """
    today = timezone.now().date()

    # Get today's device status
    today_statuses = DeviceStatus.objects.filter(date=today)

    # Get recent attendance records (last 24 hours)
    yesterday = timezone.now() - timedelta(days=1)
    recent_records = AttendanceRecordLog.objects.filter(timestamp__gte=yesterday)

    # Calculate statistics
    total_devices = Device.objects.count()
    active_devices = Device.objects.filter(status='active').count()
    inactive_devices = Device.objects.filter(status='inactive').count()
    maintenance_devices = Device.objects.filter(status='maintenance').count()

    successful_today = today_statuses.filter(success=True).count()
    failed_today = today_statuses.filter(success=False).count()

    records_today = recent_records.count()
    synced_records = recent_records.filter(sync_status=True).count()
    pending_records = recent_records.filter(sync_status=False).count()

    return {
        'date': today,
        'total_devices': total_devices,
        'device_status': {
            'active': active_devices,
            'inactive': inactive_devices,
            'maintenance': maintenance_devices
        },
        'processing_status': {
            'successful_today': successful_today,
            'failed_today': failed_today,
            'success_rate': round((successful_today / max(successful_today + failed_today, 1)) * 100, 1)
        },
        'attendance_records': {
            'total_today': records_today,
            'synced': synced_records,
            'pending': pending_records
        }
    }
