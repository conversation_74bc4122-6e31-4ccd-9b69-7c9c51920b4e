from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
import random
from device_app.models import Device, AttendanceRecordLog, DeviceStatus

class Command(BaseCommand):
    help = 'Create sample device data for testing the admin interface'

    def add_arguments(self, parser):
        parser.add_argument(
            '--devices',
            type=int,
            default=5,
            help='Number of devices to create (default: 5)'
        )
        parser.add_argument(
            '--records',
            type=int,
            default=50,
            help='Number of attendance records to create per device (default: 50)'
        )

    def handle(self, *args, **options):
        devices_count = options['devices']
        records_count = options['records']
        
        self.stdout.write(
            self.style.SUCCESS(f'Creating {devices_count} sample devices...')
        )
        
        # Sample device data
        locations = [
            'Main Building - Floor 1',
            'Main Building - Floor 2', 
            'Annex Building - Entrance',
            'Cafeteria',
            'IT Department',
            'HR Department',
            'Finance Department',
            'Reception Area'
        ]
        
        devices_created = 0
        
        for i in range(devices_count):
            device = Device.objects.create(
                display_name=f'Attendance Device {i+1:02d}',
                location=random.choice(locations),
                serial_num=f'ATT{i+1:04d}',
                IP=f'192.168.1.{100+i}',
                status=random.choice(['active', 'active', 'active', 'inactive', 'maintenance']),
                pwd='admin123',
                last_successful_pull=timezone.now() - timedelta(
                    hours=random.randint(0, 48),
                    minutes=random.randint(0, 59)
                )
            )
            devices_created += 1
            
            # Create attendance records for this device
            records_created = 0
            for j in range(records_count):
                timestamp = timezone.now() - timedelta(
                    days=random.randint(0, 30),
                    hours=random.randint(8, 18),
                    minutes=random.randint(0, 59)
                )
                
                AttendanceRecordLog.objects.create(
                    device=device,
                    employee_id=f'EMP{random.randint(1001, 1050):04d}',
                    timestamp=timestamp,
                    status=random.choice(['IN', 'OUT']),
                    sync_status=random.choice([True, True, True, False])  # 75% synced
                )
                records_created += 1
            
            # Create device status records
            for k in range(7):  # Last 7 days
                date = timezone.now().date() - timedelta(days=k)
                DeviceStatus.objects.create(
                    device=device,
                    date=date,
                    success=random.choice([True, True, True, False]),  # 75% success rate
                    last_attempt=timezone.now() - timedelta(days=k, hours=random.randint(0, 23))
                )
            
            self.stdout.write(
                f'Created device: {device.display_name} with {records_created} records'
            )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {devices_created} devices with sample data!'
            )
        )
        self.stdout.write(
            self.style.WARNING(
                'You can now access the Django admin interface to manage devices.'
            )
        )
