"""
Management command to control the device processing scheduler.
"""

from django.core.management.base import BaseCommand
from device_app.scheduler import (
    start_device_scheduler, 
    stop_device_scheduler, 
    get_scheduler_status,
    run_device_processing_now
)

class Command(BaseCommand):
    help = 'Control the device processing scheduler'

    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['start', 'stop', 'status', 'run'],
            help='Action to perform: start, stop, status, or run (manual execution)'
        )
        parser.add_argument(
            '--hour',
            type=int,
            default=22,
            help='Hour to run the scheduled job (24-hour format, default: 22)'
        )
        parser.add_argument(
            '--minute',
            type=int,
            default=0,
            help='Minute to run the scheduled job (default: 0)'
        )
        parser.add_argument(
            '--timezone',
            type=str,
            default='UTC',
            help='Timezone for scheduling (default: UTC)'
        )

    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'start':
            self.start_scheduler(options)
        elif action == 'stop':
            self.stop_scheduler()
        elif action == 'status':
            self.show_status()
        elif action == 'run':
            self.run_now()
    
    def start_scheduler(self, options):
        """Start the scheduler"""
        hour = options['hour']
        minute = options['minute']
        timezone_str = options['timezone']
        
        self.stdout.write(f"Starting scheduler to run daily at {hour:02d}:{minute:02d} {timezone_str}")
        
        success = start_device_scheduler(
            hour=hour,
            minute=minute,
            timezone_str=timezone_str
        )
        
        if success:
            self.stdout.write(
                self.style.SUCCESS("Scheduler started successfully!")
            )
        else:
            self.stdout.write(
                self.style.ERROR("Failed to start scheduler")
            )
    
    def stop_scheduler(self):
        """Stop the scheduler"""
        self.stdout.write("Stopping scheduler...")
        
        success = stop_device_scheduler()
        
        if success:
            self.stdout.write(
                self.style.SUCCESS("Scheduler stopped successfully!")
            )
        else:
            self.stdout.write(
                self.style.ERROR("Failed to stop scheduler")
            )
    
    def show_status(self):
        """Show scheduler status"""
        status = get_scheduler_status()
        
        self.stdout.write("Scheduler Status")
        self.stdout.write("=" * 30)
        
        if status['running']:
            self.stdout.write(self.style.SUCCESS("Status: RUNNING"))
            
            if status['jobs']:
                self.stdout.write("\nScheduled Jobs:")
                for job in status['jobs']:
                    self.stdout.write(f"  Job: {job['name']} (ID: {job['id']})")
                    self.stdout.write(f"  Next Run: {job['next_run']}")
                    self.stdout.write(f"  Trigger: {job['trigger']}")
            else:
                self.stdout.write("No jobs scheduled")
        else:
            self.stdout.write(self.style.WARNING("Status: STOPPED"))
            self.stdout.write("Use 'start' action to start the scheduler")
    
    def run_now(self):
        """Run device processing manually"""
        self.stdout.write("Running device processing manually...")
        
        result = run_device_processing_now()
        
        if result['success']:
            self.stdout.write(self.style.SUCCESS("Manual execution completed!"))
            
            if result['results']:
                results = result['results']
                self.stdout.write(f"Total Devices: {results['total_devices']}")
                self.stdout.write(f"Successful Pings: {results['successful_pings']}")
                self.stdout.write(f"Successful Data Retrievals: {results['successful_data_retrievals']}")
                self.stdout.write(f"Total Records Retrieved: {results['total_records_retrieved']}")
                
                if results['errors']:
                    self.stdout.write(self.style.WARNING(f"Errors: {len(results['errors'])}"))
        else:
            self.stdout.write(
                self.style.ERROR(f"Manual execution failed: {result['message']}")
            )
