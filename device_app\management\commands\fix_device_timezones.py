from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
import random
from device_app.models import Device, AttendanceRecordLog, DeviceStatus

class Command(BaseCommand):
    help = 'Fix timezone issues in device data and recreate sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear all existing device data before creating new data'
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing device data...')
            AttendanceRecordLog.objects.all().delete()
            DeviceStatus.objects.all().delete()
            Device.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Existing data cleared.'))
        
        self.stdout.write('Creating devices with proper timezone handling...')
        
        # Sample device data
        locations = [
            'Main Building - Floor 1',
            'Main Building - Floor 2', 
            'Annex Building - Entrance',
            'Cafeteria',
            'IT Department',
            'HR Department',
            'Finance Department',
            'Reception Area'
        ]
        
        devices_created = 0
        
        for i in range(5):  # Create 5 devices
            # Use timezone.now() for proper timezone handling
            last_pull_time = timezone.now() - timedelta(
                hours=random.randint(0, 48),
                minutes=random.randint(0, 59)
            )
            
            device = Device.objects.create(
                display_name=f'Attendance Device {i+1:02d}',
                location=random.choice(locations),
                serial_num=f'ATT{i+1:04d}',
                IP=f'192.168.1.{100+i}',
                status=random.choice(['active', 'active', 'active', 'inactive', 'maintenance']),
                pwd='admin123',
                last_successful_pull=last_pull_time
            )
            devices_created += 1
            
            # Create attendance records for this device
            records_created = 0
            for j in range(20):  # Create 20 records per device
                # Create timezone-aware datetime
                record_time = timezone.now() - timedelta(
                    days=random.randint(0, 7),  # Last 7 days only
                    hours=random.randint(8, 18),
                    minutes=random.randint(0, 59)
                )
                
                try:
                    AttendanceRecordLog.objects.create(
                        device=device,
                        employee_id=f'EMP{random.randint(1001, 1020):04d}',
                        timestamp=record_time,
                        status=random.choice(['IN', 'OUT']),
                        sync_status=random.choice([True, True, True, False])  # 75% synced
                    )
                    records_created += 1
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f'Error creating record: {e}')
                    )
            
            # Create device status records
            for k in range(7):  # Last 7 days
                status_date = timezone.now().date() - timedelta(days=k)
                last_attempt_time = timezone.now() - timedelta(
                    days=k, 
                    hours=random.randint(0, 23)
                )
                
                try:
                    DeviceStatus.objects.create(
                        device=device,
                        date=status_date,
                        success=random.choice([True, True, True, False]),  # 75% success rate
                        last_attempt=last_attempt_time
                    )
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f'Error creating device status: {e}')
                    )
            
            self.stdout.write(
                f'Created device: {device.display_name} with {records_created} records'
            )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {devices_created} devices with timezone-aware data!'
            )
        )
        self.stdout.write(
            self.style.WARNING(
                'You can now access the Django admin interface to manage devices.'
            )
        )
