"""
Management command to manually trigger device processing.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from device_app.background_tasks import process_all_devices, get_device_processing_summary
from device_app.models import Device

class Command(BaseCommand):
    help = 'Manually process all devices (ping, retrieve data, update status)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--device-id',
            type=int,
            help='Process only a specific device by ID'
        )
        parser.add_argument(
            '--summary',
            action='store_true',
            help='Show processing summary instead of running processing'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output'
        )

    def handle(self, *args, **options):
        if options['summary']:
            self.show_summary()
            return
        
        device_id = options.get('device_id')
        verbose = options.get('verbose', False)
        
        if device_id:
            self.process_single_device(device_id, verbose)
        else:
            self.process_all_devices(verbose)
    
    def show_summary(self):
        """Show processing summary"""
        self.stdout.write(self.style.SUCCESS('Device Processing Summary'))
        self.stdout.write('=' * 50)
        
        summary = get_device_processing_summary()
        
        self.stdout.write(f"Date: {summary['date']}")
        self.stdout.write(f"Total Devices: {summary['total_devices']}")
        
        self.stdout.write("\nDevice Status:")
        for status, count in summary['device_status'].items():
            self.stdout.write(f"  {status.title()}: {count}")
        
        self.stdout.write("\nProcessing Status:")
        for status, value in summary['processing_status'].items():
            if status == 'success_rate':
                self.stdout.write(f"  {status.replace('_', ' ').title()}: {value}%")
            else:
                self.stdout.write(f"  {status.replace('_', ' ').title()}: {value}")
        
        self.stdout.write("\nAttendance Records:")
        for record_type, count in summary['attendance_records'].items():
            self.stdout.write(f"  {record_type.replace('_', ' ').title()}: {count}")
    
    def process_single_device(self, device_id, verbose):
        """Process a single device"""
        try:
            device = Device.objects.get(id=device_id)
            self.stdout.write(f"Processing device: {device.display_name}")
            
            from device_app.background_tasks import process_device
            result = process_device(device)
            
            self.stdout.write(self.style.SUCCESS(f"Device processed successfully"))
            
            if verbose:
                self.stdout.write(f"Ping Success: {result['ping_success']}")
                self.stdout.write(f"Data Retrieval Success: {result['data_retrieval_success']}")
                self.stdout.write(f"Records Retrieved: {result['records_retrieved']}")
                
                if result['errors']:
                    self.stdout.write(self.style.WARNING("Errors:"))
                    for error in result['errors']:
                        self.stdout.write(f"  - {error}")
            
        except Device.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Device with ID {device_id} not found")
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error processing device: {e}")
            )
    
    def process_all_devices(self, verbose):
        """Process all devices"""
        self.stdout.write("Starting device processing for all devices...")
        
        start_time = timezone.now()
        
        try:
            results = process_all_devices()
            
            end_time = timezone.now()
            duration = (end_time - start_time).total_seconds()
            
            self.stdout.write(self.style.SUCCESS("Device processing completed!"))
            self.stdout.write(f"Duration: {duration:.2f} seconds")
            self.stdout.write(f"Total Devices: {results['total_devices']}")
            self.stdout.write(f"Successful Pings: {results['successful_pings']}")
            self.stdout.write(f"Successful Data Retrievals: {results['successful_data_retrievals']}")
            self.stdout.write(f"Total Records Retrieved: {results['total_records_retrieved']}")
            
            if results['errors']:
                self.stdout.write(self.style.WARNING(f"Errors encountered: {len(results['errors'])}"))
                if verbose:
                    for error in results['errors']:
                        self.stdout.write(f"  - {error}")
            
            if verbose and results['device_results']:
                self.stdout.write("\nDevice Details:")
                for device_result in results['device_results']:
                    self.stdout.write(f"  {device_result['device_name']}:")
                    self.stdout.write(f"    Ping: {'✓' if device_result['ping_success'] else '✗'}")
                    self.stdout.write(f"    Data: {'✓' if device_result['data_retrieval_success'] else '✗'}")
                    self.stdout.write(f"    Records: {device_result['records_retrieved']}")
        
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Critical error during processing: {e}")
            )
