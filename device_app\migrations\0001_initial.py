# Generated by Django 5.2.3 on 2025-06-21 14:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Device',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('display_name', models.CharField(max_length=100, verbose_name='Display Name')),
                ('location', models.CharField(blank=True, max_length=200, null=True, verbose_name='Location')),
                ('serial_num', models.CharField(max_length=50, unique=True, verbose_name='Serial Number')),
                ('IP', models.GenericIPAddressField(verbose_name='IP Address')),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('maintenance', 'Under Maintenance')], default='active', max_length=20, verbose_name='Status')),
                ('pwd', models.CharField(blank=True, max_length=100, null=True, verbose_name='Password')),
                ('last_successful_pull', models.DateTimeField(blank=True, null=True, verbose_name='Last Successful Pull')),
            ],
            options={
                'verbose_name': 'Device',
                'verbose_name_plural': 'Devices',
            },
        ),
        migrations.CreateModel(
            name='AttendanceRecordLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(max_length=50)),
                ('timestamp', models.DateTimeField()),
                ('status', models.CharField(choices=[('IN', 'Check In'), ('OUT', 'Check Out')], max_length=20)),
                ('sync_status', models.BooleanField(default=False)),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device_app.device')),
            ],
            options={
                'ordering': ['-timestamp'],
                'unique_together': {('device', 'employee_id', 'timestamp')},
            },
        ),
        migrations.CreateModel(
            name='DeviceStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('success', models.BooleanField(default=False)),
                ('last_attempt', models.DateTimeField(auto_now=True)),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device_app.device')),
            ],
            options={
                'unique_together': {('device', 'date')},
            },
        ),
    ]
