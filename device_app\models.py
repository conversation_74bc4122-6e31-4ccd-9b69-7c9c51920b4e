from django.db import models
from django.utils import timezone

class Device(models.Model):
    # Choices for the Status field
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('maintenance', 'Under Maintenance'),
    ]

    # Fields
    display_name = models.CharField(max_length=100, verbose_name="Display Name")
    location = models.CharField(max_length=200, verbose_name="Location", null=True, blank=True)
    serial_num = models.CharField(max_length=50, unique=True, verbose_name="Serial Number")
    IP = models.GenericIPAddressField(verbose_name="IP Address")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="Status")
    pwd = models.CharField(max_length=100, blank=True, null=True, verbose_name="Password")
    last_successful_pull = models.DateTimeField(null=True, blank=True, verbose_name="Last Successful Pull")
    
    def __str__(self):
        return f' {self.display_name} {self.location}'

    class Meta:
        verbose_name = "Device"
        verbose_name_plural = "Devices"
        
class AttendanceRecordLog(models.Model):
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    employee_id = models.CharField(max_length=50)
    timestamp = models.DateTimeField()
    status = models.CharField(max_length=20, choices=[
        ('IN', 'Check In'),
        ('OUT', 'Check Out'),
    ])
    sync_status = models.BooleanField(default=False)
    
    class Meta:
        ordering = ['-timestamp']
        unique_together = ('device', 'employee_id', 'timestamp')
        

class DeviceStatus(models.Model):
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    date = models.DateField()
    success = models.BooleanField(default=False)
    last_attempt = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('device', 'date')

    def __str__(self):
        return f"{self.device} - {self.date} - {'Success' if self.success else 'Failed'}"      
