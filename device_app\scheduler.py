"""
Scheduler for device management background tasks.
Uses APScheduler to run device processing tasks on a schedule.
"""

import logging
import atexit
from datetime import datetime
from typing import Optional
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
from django.conf import settings
from django.utils import timezone
from .background_tasks import process_all_devices, get_device_processing_summary

# Configure logging
logger = logging.getLogger('device_scheduler')

class DeviceScheduler:
    """
    Scheduler for device management background tasks.
    """
    
    def __init__(self):
        self.scheduler = None
        self.is_running = False
        
    def _job_listener(self, event):
        """
        Listen to job execution events for logging.
        """
        if event.exception:
            logger.error(f"Job {event.job_id} crashed: {event.exception}")
        else:
            logger.info(f"Job {event.job_id} executed successfully")
    
    def _scheduled_device_processing(self):
        """
        Wrapper function for scheduled device processing.
        Includes additional logging and error handling.
        """
        try:
            logger.info("Starting scheduled device processing")
            
            # Run the main processing function
            results = process_all_devices()
            
            # Log results summary
            logger.info(
                f"Scheduled processing completed: "
                f"{results['successful_pings']}/{results['total_devices']} devices online, "
                f"{results['total_records_retrieved']} records retrieved"
            )
            
            # Log any errors
            if results['errors']:
                for error in results['errors']:
                    logger.warning(f"Processing error: {error}")
            
            return results
            
        except Exception as e:
            logger.error(f"Critical error in scheduled device processing: {e}")
            raise
    
    def start_scheduler(self, 
                       hour: int = 22, 
                       minute: int = 0, 
                       timezone_str: str = 'UTC') -> bool:
        """
        Start the background scheduler.
        
        Args:
            hour: Hour to run the job (24-hour format, default: 22 = 10 PM)
            minute: Minute to run the job (default: 0)
            timezone_str: Timezone for scheduling (default: UTC)
            
        Returns:
            bool: True if scheduler started successfully, False otherwise
        """
        try:
            if self.is_running:
                logger.warning("Scheduler is already running")
                return True
            
            # Create scheduler instance
            self.scheduler = BackgroundScheduler()
            
            # Add job listener for monitoring
            self.scheduler.add_listener(self._job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)
            
            # Create cron trigger for daily execution
            trigger = CronTrigger(
                hour=hour,
                minute=minute,
                timezone=timezone_str
            )
            
            # Add the main processing job
            self.scheduler.add_job(
                func=self._scheduled_device_processing,
                trigger=trigger,
                id='device_processing_job',
                name='Daily Device Processing',
                replace_existing=True,
                max_instances=1  # Prevent overlapping executions
            )
            
            # Start the scheduler
            self.scheduler.start()
            self.is_running = True
            
            logger.info(f"Device scheduler started successfully. Next run: {hour:02d}:{minute:02d} {timezone_str}")
            
            # Register shutdown handler
            atexit.register(self.stop_scheduler)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start scheduler: {e}")
            return False
    
    def stop_scheduler(self) -> bool:
        """
        Stop the background scheduler.
        
        Returns:
            bool: True if scheduler stopped successfully, False otherwise
        """
        try:
            if not self.is_running or not self.scheduler:
                logger.warning("Scheduler is not running")
                return True
            
            self.scheduler.shutdown(wait=True)
            self.scheduler = None
            self.is_running = False
            
            logger.info("Device scheduler stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop scheduler: {e}")
            return False
    
    def get_scheduler_status(self) -> dict:
        """
        Get current scheduler status and job information.
        
        Returns:
            Dictionary with scheduler status
        """
        if not self.is_running or not self.scheduler:
            return {
                'running': False,
                'jobs': [],
                'next_run': None
            }
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
        
        return {
            'running': True,
            'jobs': jobs,
            'next_run': jobs[0]['next_run'] if jobs else None
        }
    
    def run_job_now(self) -> dict:
        """
        Manually trigger the device processing job.
        
        Returns:
            Dictionary with job execution results
        """
        try:
            logger.info("Manually triggering device processing job")
            results = self._scheduled_device_processing()
            return {
                'success': True,
                'results': results,
                'message': 'Job executed successfully'
            }
        except Exception as e:
            error_msg = f"Manual job execution failed: {e}"
            logger.error(error_msg)
            return {
                'success': False,
                'results': None,
                'message': error_msg
            }

# Global scheduler instance
_device_scheduler = None

def get_scheduler() -> DeviceScheduler:
    """
    Get the global scheduler instance (singleton pattern).
    
    Returns:
        DeviceScheduler instance
    """
    global _device_scheduler
    if _device_scheduler is None:
        _device_scheduler = DeviceScheduler()
    return _device_scheduler

def start_device_scheduler(**kwargs) -> bool:
    """
    Start the device scheduler with optional configuration.
    
    Args:
        **kwargs: Configuration options (hour, minute, timezone_str)
        
    Returns:
        bool: True if started successfully, False otherwise
    """
    # Get configuration from Django settings if available
    scheduler_config = getattr(settings, 'DEVICE_SCHEDULER_CONFIG', {})
    
    # Merge with provided kwargs
    config = {
        'hour': 22,  # 10 PM default
        'minute': 0,
        'timezone_str': 'UTC'
    }
    config.update(scheduler_config)
    config.update(kwargs)
    
    scheduler = get_scheduler()
    return scheduler.start_scheduler(**config)

def stop_device_scheduler() -> bool:
    """
    Stop the device scheduler.
    
    Returns:
        bool: True if stopped successfully, False otherwise
    """
    scheduler = get_scheduler()
    return scheduler.stop_scheduler()

def get_scheduler_status() -> dict:
    """
    Get current scheduler status.
    
    Returns:
        Dictionary with scheduler status
    """
    scheduler = get_scheduler()
    return scheduler.get_scheduler_status()

def run_device_processing_now() -> dict:
    """
    Manually trigger device processing.
    
    Returns:
        Dictionary with execution results
    """
    scheduler = get_scheduler()
    return scheduler.run_job_now()

# Configuration helper
def configure_scheduler_from_settings():
    """
    Configure scheduler based on Django settings.
    """
    if hasattr(settings, 'DEVICE_SCHEDULER_CONFIG'):
        config = settings.DEVICE_SCHEDULER_CONFIG
        logger.info(f"Using scheduler configuration from settings: {config}")
        return config
    else:
        # Default configuration
        default_config = {
            'hour': 22,  # 10 PM
            'minute': 0,
            'timezone_str': 'UTC'
        }
        logger.info(f"Using default scheduler configuration: {default_config}")
        return default_config
