{% extends "admin/change_list.html" %}
{% load admin_urls static admin_list %}

{% block date_hierarchy %}
    {# Override date_hierarchy to prevent timezone errors #}
    {% comment %}
    Original date hierarchy causes timezone issues in MySQL
    {% if cl.date_hierarchy %}{% date_hierarchy cl %}{% endif %}
    {% endcomment %}
    
    {# Custom date filtering can be added here if needed #}
    <div style="padding: 10px; background: #f8f9fa; border-bottom: 1px solid #dee2e6; margin-bottom: 10px;">
        <strong>Attendance Records</strong> - Use the filter sidebar to filter by date and device
    </div>
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <style>
        .attendance-record-stats {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .stat-item {
            display: inline-block;
            margin-right: 30px;
            text-align: center;
        }
        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #007cba;
        }
        .stat-label {
            font-size: 11px;
            color: #666;
            text-transform: uppercase;
        }
        .status-in { color: #28a745; }
        .status-out { color: #dc3545; }
        .sync-synced { color: #28a745; }
        .sync-pending { color: #ffc107; }
    </style>
{% endblock %}

{% block content_title %}
    <h1>Attendance Record Management</h1>
    <div class="attendance-record-stats">
        <div class="stat-item">
            <div class="stat-number status-in" id="checkin-count">-</div>
            <div class="stat-label">Check-ins Today</div>
        </div>
        <div class="stat-item">
            <div class="stat-number status-out" id="checkout-count">-</div>
            <div class="stat-label">Check-outs Today</div>
        </div>
        <div class="stat-item">
            <div class="stat-number sync-synced" id="synced-count">-</div>
            <div class="stat-label">Synced Records</div>
        </div>
        <div class="stat-item">
            <div class="stat-number sync-pending" id="pending-count">-</div>
            <div class="stat-label">Pending Sync</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="total-devices">-</div>
            <div class="stat-label">Active Devices</div>
        </div>
    </div>
{% endblock %}

{% block result_list %}
    {{ block.super }}
    <script>
        // Load attendance record statistics
        document.addEventListener('DOMContentLoaded', function() {
            // This would typically fetch data via AJAX
            // For now, we'll use placeholder values
            document.getElementById('checkin-count').textContent = '{{ checkin_today|default:"0" }}';
            document.getElementById('checkout-count').textContent = '{{ checkout_today|default:"0" }}';
            document.getElementById('synced-count').textContent = '{{ synced_records|default:"0" }}';
            document.getElementById('pending-count').textContent = '{{ pending_records|default:"0" }}';
            document.getElementById('total-devices').textContent = '{{ active_devices|default:"0" }}';
        });
    </script>
{% endblock %}
