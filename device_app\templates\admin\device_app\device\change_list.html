{% extends "admin/change_list.html" %}
{% load admin_urls static admin_list %}

{% block date_hierarchy %}
    {# Override date_hierarchy to prevent timezone errors #}
    {% comment %}
    {% if cl.date_hierarchy %}{% date_hierarchy cl %}{% endif %}
    {% endcomment %}
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <style>
        .device-stats {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .stat-item {
            display: inline-block;
            margin-right: 30px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007cba;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
        .status-active { color: #28a745; }
        .status-inactive { color: #dc3545; }
        .status-maintenance { color: #ffc107; }
    </style>
{% endblock %}

{% block content_title %}
    <h1>Device Management Dashboard</h1>
    <div class="device-stats">
        <div class="stat-item">
            <div class="stat-number status-active" id="active-count">-</div>
            <div class="stat-label">Active Devices</div>
        </div>
        <div class="stat-item">
            <div class="stat-number status-inactive" id="inactive-count">-</div>
            <div class="stat-label">Inactive Devices</div>
        </div>
        <div class="stat-item">
            <div class="stat-number status-maintenance" id="maintenance-count">-</div>
            <div class="stat-label">Under Maintenance</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="total-records">-</div>
            <div class="stat-label">Total Records Today</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="online-devices">-</div>
            <div class="stat-label">Online Devices</div>
        </div>
    </div>
{% endblock %}

{% block result_list %}
    {{ block.super }}
    <script>
        // Load device statistics
        document.addEventListener('DOMContentLoaded', function() {
            // This would typically fetch data via AJAX
            // For now, we'll use placeholder values
            document.getElementById('active-count').textContent = '{{ active_devices_count|default:"0" }}';
            document.getElementById('inactive-count').textContent = '{{ inactive_devices_count|default:"0" }}';
            document.getElementById('maintenance-count').textContent = '{{ maintenance_devices_count|default:"0" }}';
            document.getElementById('total-records').textContent = '{{ total_records_today|default:"0" }}';
            document.getElementById('online-devices').textContent = '{{ online_devices_count|default:"0" }}';
        });
    </script>
{% endblock %}
