{% extends "admin/change_list.html" %}
{% load admin_urls static admin_list %}

{% block date_hierarchy %}
    {# Override date_hierarchy to prevent timezone errors #}
    {% comment %}
    Original date hierarchy causes timezone issues in MySQL
    {% if cl.date_hierarchy %}{% date_hierarchy cl %}{% endif %}
    {% endcomment %}
    
    <div style="padding: 10px; background: #f8f9fa; border-bottom: 1px solid #dee2e6; margin-bottom: 10px;">
        <strong>Device Status History</strong> - Use the filter sidebar to filter by date and device
    </div>
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <style>
        .device-status-stats {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .stat-item {
            display: inline-block;
            margin-right: 30px;
            text-align: center;
        }
        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #007cba;
        }
        .stat-label {
            font-size: 11px;
            color: #666;
            text-transform: uppercase;
        }
        .status-success { color: #28a745; }
        .status-failed { color: #dc3545; }
    </style>
{% endblock %}

{% block content_title %}
    <h1>Device Status Monitoring</h1>
    <div class="device-status-stats">
        <div class="stat-item">
            <div class="stat-number status-success" id="success-today">-</div>
            <div class="stat-label">Successful Today</div>
        </div>
        <div class="stat-item">
            <div class="stat-number status-failed" id="failed-today">-</div>
            <div class="stat-label">Failed Today</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="total-attempts">-</div>
            <div class="stat-label">Total Attempts</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="success-rate">-</div>
            <div class="stat-label">Success Rate %</div>
        </div>
    </div>
{% endblock %}

{% block result_list %}
    {{ block.super }}
    <script>
        // Load device status statistics
        document.addEventListener('DOMContentLoaded', function() {
            // This would typically fetch data via AJAX
            // For now, we'll use placeholder values
            document.getElementById('success-today').textContent = '{{ success_today|default:"0" }}';
            document.getElementById('failed-today').textContent = '{{ failed_today|default:"0" }}';
            document.getElementById('total-attempts').textContent = '{{ total_attempts|default:"0" }}';
            document.getElementById('success-rate').textContent = '{{ success_rate|default:"0" }}';
        });
    </script>
{% endblock %}
