"""
Views for device management system.
"""

from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from django.http import JsonResponse
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from .models import Device, AttendanceRecordLog
from .background_tasks import get_device_processing_summary
from .scheduler import get_scheduler_status, run_device_processing_now

@staff_member_required
def device_dashboard(request):
    """
    Main dashboard for device management.
    """
    # Get device statistics
    total_devices = Device.objects.count()
    active_devices = Device.objects.filter(status='active').count()
    inactive_devices = Device.objects.filter(status='inactive').count()
    maintenance_devices = Device.objects.filter(status='maintenance').count()

    # Get recent activity
    recent_records = AttendanceRecordLog.objects.select_related('device').order_by('-timestamp')[:10]

    # Get scheduler status
    scheduler_status = get_scheduler_status()

    # Get processing summary
    processing_summary = get_device_processing_summary()

    context = {
        'total_devices': total_devices,
        'active_devices': active_devices,
        'inactive_devices': inactive_devices,
        'maintenance_devices': maintenance_devices,
        'recent_records': recent_records,
        'scheduler_status': scheduler_status,
        'processing_summary': processing_summary,
    }

    return render(request, 'device_app/dashboard.html', context)

@staff_member_required
@require_http_methods(["POST"])
def trigger_device_processing(request):
    """
    AJAX endpoint to manually trigger device processing.
    """
    try:
        result = run_device_processing_now()
        return JsonResponse(result)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error triggering device processing: {str(e)}'
        })

@staff_member_required
def device_status_api(request):
    """
    API endpoint to get current device status.
    """
    devices = Device.objects.all().values(
        'id', 'display_name', 'location', 'status', 'last_successful_pull'
    )

    device_list = []
    for device in devices:
        # Convert datetime to string for JSON serialization
        last_pull = device['last_successful_pull']
        if last_pull:
            last_pull = last_pull.isoformat()

        device_list.append({
            'id': device['id'],
            'name': device['display_name'],
            'location': device['location'],
            'status': device['status'],
            'last_pull': last_pull
        })

    return JsonResponse({
        'devices': device_list,
        'timestamp': timezone.now().isoformat()
    })
