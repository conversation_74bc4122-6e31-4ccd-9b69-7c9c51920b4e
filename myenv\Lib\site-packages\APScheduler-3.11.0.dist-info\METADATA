Metadata-Version: 2.1
Name: APScheduler
Version: 3.11.0
Summary: In-process task scheduler with Cron-like capabilities
Author-email: <PERSON> <<EMAIL>>
License: MIT
Project-URL: Documentation, https://apscheduler.readthedocs.io/en/3.x/
Project-URL: Changelog, https://apscheduler.readthedocs.io/en/3.x/versionhistory.html
Project-URL: Source code, https://github.com/agronholm/apscheduler
Project-URL: Issue tracker, https://github.com/agronholm/apscheduler/issues
Keywords: scheduling,cron
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE.txt
Requires-Dist: tzlocal>=3.0
Requires-Dist: backports.zoneinfo; python_version < "3.9"
Provides-Extra: etcd
Requires-Dist: etcd3; extra == "etcd"
Requires-Dist: protobuf<=3.21.0; extra == "etcd"
Provides-Extra: gevent
Requires-Dist: gevent; extra == "gevent"
Provides-Extra: mongodb
Requires-Dist: pymongo>=3.0; extra == "mongodb"
Provides-Extra: redis
Requires-Dist: redis>=3.0; extra == "redis"
Provides-Extra: rethinkdb
Requires-Dist: rethinkdb>=2.4.0; extra == "rethinkdb"
Provides-Extra: sqlalchemy
Requires-Dist: sqlalchemy>=1.4; extra == "sqlalchemy"
Provides-Extra: tornado
Requires-Dist: tornado>=4.3; extra == "tornado"
Provides-Extra: twisted
Requires-Dist: twisted; extra == "twisted"
Provides-Extra: zookeeper
Requires-Dist: kazoo; extra == "zookeeper"
Provides-Extra: test
Requires-Dist: APScheduler[etcd,mongodb,redis,rethinkdb,sqlalchemy,tornado,zookeeper]; extra == "test"
Requires-Dist: pytest; extra == "test"
Requires-Dist: anyio>=4.5.2; extra == "test"
Requires-Dist: PySide6; (platform_python_implementation == "CPython" and python_version < "3.14") and extra == "test"
Requires-Dist: gevent; python_version < "3.14" and extra == "test"
Requires-Dist: pytz; extra == "test"
Requires-Dist: twisted; python_version < "3.14" and extra == "test"
Provides-Extra: doc
Requires-Dist: packaging; extra == "doc"
Requires-Dist: sphinx; extra == "doc"
Requires-Dist: sphinx-rtd-theme>=1.3.0; extra == "doc"

.. image:: https://github.com/agronholm/apscheduler/workflows/Python%20codeqa/test/badge.svg?branch=3.x
  :target: https://github.com/agronholm/apscheduler/actions?query=workflow%3A%22Python+codeqa%2Ftest%22+branch%3A3.x
  :alt: Build Status
.. image:: https://coveralls.io/repos/github/agronholm/apscheduler/badge.svg?branch=3.x
  :target: https://coveralls.io/github/agronholm/apscheduler?branch=3.x
  :alt: Code Coverage
.. image:: https://readthedocs.org/projects/apscheduler/badge/?version=3.x
  :target: https://apscheduler.readthedocs.io/en/master/?badge=3.x
  :alt: Documentation

Advanced Python Scheduler (APScheduler) is a Python library that lets you schedule your Python code
to be executed later, either just once or periodically. You can add new jobs or remove old ones on
the fly as you please. If you store your jobs in a database, they will also survive scheduler
restarts and maintain their state. When the scheduler is restarted, it will then run all the jobs
it should have run while it was offline [#f1]_.

Among other things, APScheduler can be used as a cross-platform, application specific replacement
to platform specific schedulers, such as the cron daemon or the Windows task scheduler. Please
note, however, that APScheduler is **not** a daemon or service itself, nor does it come with any
command line tools. It is primarily meant to be run inside existing applications. That said,
APScheduler does provide some building blocks for you to build a scheduler service or to run a
dedicated scheduler process.

APScheduler has three built-in scheduling systems you can use:

* Cron-style scheduling (with optional start/end times)
* Interval-based execution (runs jobs on even intervals, with optional start/end times)
* One-off delayed execution (runs jobs once, on a set date/time)

You can mix and match scheduling systems and the backends where the jobs are stored any way you
like. Supported backends for storing jobs include:

* Memory
* `SQLAlchemy <http://www.sqlalchemy.org/>`_ (any RDBMS supported by SQLAlchemy works)
* `MongoDB <http://www.mongodb.org/>`_
* `Redis <http://redis.io/>`_
* `RethinkDB <https://www.rethinkdb.com/>`_
* `ZooKeeper <https://zookeeper.apache.org/>`_
* `Etcd <https://etcd.io/>`_

APScheduler also integrates with several common Python frameworks, like:

* `asyncio <http://docs.python.org/3.4/library/asyncio.html>`_ (:pep:`3156`)
* `gevent <http://www.gevent.org/>`_
* `Tornado <http://www.tornadoweb.org/>`_
* `Twisted <http://twistedmatrix.com/>`_
* `Qt <http://qt-project.org/>`_ (using either
  `PyQt <http://www.riverbankcomputing.com/software/pyqt/intro>`_ ,
  `PySide6 <https://wiki.qt.io/Qt_for_Python>`_ ,
  `PySide2 <https://wiki.qt.io/Qt_for_Python>`_ or
  `PySide <http://qt-project.org/wiki/PySide>`_)

There are third party solutions for integrating APScheduler with other frameworks:

* `Django <https://github.com/jarekwg/django-apscheduler>`_
* `Flask <https://github.com/viniciuschiele/flask-apscheduler>`_


.. [#f1] The cutoff period for this is also configurable.


Documentation
-------------

Documentation can be found `here <https://apscheduler.readthedocs.io/>`_.


Source
------

The source can be browsed at `Github <https://github.com/agronholm/apscheduler/tree/3.x>`_.


Reporting bugs
--------------

A `bug tracker <https://github.com/agronholm/apscheduler/issues>`_ is provided by Github.


Getting help
------------

If you have problems or other questions, you can either:

* Ask in the `apscheduler <https://gitter.im/apscheduler/Lobby>`_ room on Gitter
* Ask on the `APScheduler GitHub discussion forum <https://github.com/agronholm/apscheduler/discussions>`_, or
* Ask on `StackOverflow <http://stackoverflow.com/questions/tagged/apscheduler>`_ and tag your
  question with the ``apscheduler`` tag
