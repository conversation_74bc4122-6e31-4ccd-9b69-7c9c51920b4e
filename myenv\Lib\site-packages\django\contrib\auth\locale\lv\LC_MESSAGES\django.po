# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023-2025
# <PERSON><PERSON> <<EMAIL>>, 2017,2022
# <PERSON><PERSON>oro<PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2019,2021
# <AUTHOR> <EMAIL>, 2016
# Pēteris Caune, 2023
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 11:30-0500\n"
"PO-Revision-Date: 2025-04-01 15:04-0500\n"
"Last-Translator: Edgar<PERSON> Voroboks <<EMAIL>>, 2023-2025\n"
"Language-Team: Latvian (http://app.transifex.com/django/django/language/"
"lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : "
"2);\n"

msgid "Personal info"
msgstr "Personīgā informācija"

msgid "Permissions"
msgstr "Tiesības"

msgid "Important dates"
msgstr "Svarīgi datumi"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "%(name)s objekts ar primāro atslēgu %(key)r neeksistē."

msgid "Conflicting form data submitted. Please try again."
msgstr "Iesniegti pretrunīgi veidlapas dati. Lūdzu mēģiniet vēlreiz."

msgid "Password changed successfully."
msgstr "Parole nomainīta sekmīgi."

msgid "Password-based authentication was disabled."
msgstr "Paroles bāzēta autentifikācija tika atspējota."

#, python-format
msgid "Change password: %s"
msgstr "Paroles maiņa: %s"

#, python-format
msgid "Set password: %s"
msgstr "Iestatīt paroli: %s"

msgid "Authentication and Authorization"
msgstr "Autentifikācija un autorizācija"

msgid "password"
msgstr "parole"

msgid "last login"
msgstr "pēdējoreiz pieslēdzies"

msgid "Invalid password format or unknown hashing algorithm."
msgstr "Nederīgs paroles formāts vai nezināms hash algoritms."

msgid "No password set."
msgstr "Nav norādīta parole"

msgid "Reset password"
msgstr "Atiestatīt paroli"

msgid "Set password"
msgstr "Iestatīt paroli"

msgid "The two password fields didn’t match."
msgstr "Paroles lauki nesakrita."

msgid "Password"
msgstr "Parole"

msgid "Password confirmation"
msgstr "Paroles apstiprinājums"

msgid "Enter the same password as before, for verification."
msgstr "Ievadi iepriekš norādīto paroli verifikācijai."

msgid ""
"Whether the user will be able to authenticate using a password or not. If "
"disabled, they may still be able to authenticate using other backends, such "
"as Single Sign-On or LDAP."
msgstr ""
"Vai lietotājs varēs autentificēties, izmantojot paroli, vai nē. Ja "
"atspējots, lietotāji joprojām varēs autentificēties, izmantojot citas "
"autentifikācijas sistēmas, piemēram, vienreizējo pierakstīšanos (Single Sign-"
"On) vai LDAP."

msgid "Password-based authentication"
msgstr "Paroles bāzēta autentifikācija"

msgid "Enabled"
msgstr "Iespējots"

msgid "Disabled"
msgstr "Atspējots"

msgid ""
"Raw passwords are not stored, so there is no way to see the user’s password."
msgstr ""
"Nešifrētas paroles netiek saglabātas, tāpēc nav iespējams apskatīt lietotāja "
"paroli."

msgid ""
"Enable password-based authentication for this user by setting a password."
msgstr ""
"Iespējojiet šim lietotājam paroles balstītu autentifikāciju, iestatot paroli."

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"Lūdzu ievadiet korektu informāciju laukos %(username)s un parole. Ņemiet "
"vērā, ka abi ievades lauki ir reģistrjutīgi."

msgid "This account is inactive."
msgstr "Šis konts nav aktīvs."

msgid "Email"
msgstr "E-pasts"

msgid "New password"
msgstr "Jaunā parole"

msgid "New password confirmation"
msgstr "Jaunā parole vēlreiz"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr ""
"Jūsu iepriekšējā parole netika ievadīta korekti. Lūdzu ievadiet to atkārtoti."

msgid "Old password"
msgstr "Vecā parole"

msgid "algorithm"
msgstr "algoritms"

msgid "iterations"
msgstr "iterācijas"

msgid "salt"
msgstr "salt"

msgid "hash"
msgstr "hash"

msgid "variety"
msgstr "dažādība"

msgid "version"
msgstr "versija"

msgid "memory cost"
msgstr "atmiņas izmaksas"

msgid "time cost"
msgstr "laika izmaksas"

msgid "parallelism"
msgstr "paralēlisms"

msgid "work factor"
msgstr "darba faktors"

msgid "checksum"
msgstr "kontrolsumma"

msgid "block size"
msgstr "bloka izmērs"

msgid "name"
msgstr "nosaukums"

msgid "content type"
msgstr "satura tips"

msgid "codename"
msgstr "kods"

msgid "permission"
msgstr "tiesība"

msgid "permissions"
msgstr "tiesības"

msgid "group"
msgstr "grupa"

msgid "groups"
msgstr "grupas"

msgid "superuser status"
msgstr "superlietotāja statuss"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr ""
"Nosaka, ka lietotājam ir visas tiesības arī bez to atsevišķas piešķiršanas."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""
"Grupas, kurām lietotājs pieder. Lietotājam būs visas tiesības, kuras ir "
"piešķirtas grupām, kurām tas pieder."

msgid "user permissions"
msgstr "lietotāja tiesības"

msgid "Specific permissions for this user."
msgstr "Lietotāja specifiskās tiesības."

msgid "username"
msgstr "lietotājvārds"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr "Obligāts. 150 vai mazāk zīmes. Tikai burti, cipari un @/./+/-/_ ."

msgid "A user with that username already exists."
msgstr "Lietotājs ar šādu lietotāja vārdu jau eksistē."

msgid "first name"
msgstr "vārds"

msgid "last name"
msgstr "uzvārds"

msgid "email address"
msgstr "e-pasta adrese"

msgid "staff status"
msgstr "personāla statuss"

msgid "Designates whether the user can log into this admin site."
msgstr ""
"Atzīmējiet, ja vēlaties, lai lietotājs var pieslēgties administrācijas lapā."

msgid "active"
msgstr "aktīvs"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"Nosaka, vai lietotājs ir aktīvs. Lietojiet šo kā alternatīvu kontu dzēšanai."

msgid "date joined"
msgstr "datums, kad pievienojies"

msgid "user"
msgstr "lietotājs"

msgid "users"
msgstr "lietotāji"

#, python-format
msgid "This password is too short. It must contain at least %d character."
msgid_plural ""
"This password is too short. It must contain at least %d characters."
msgstr[0] "Šī parole ir pārāk īsa. Tai ir jābūt vismaz %d zīmes garai."
msgstr[1] "Šī parole ir pārāk īsa. Tai ir jābūt vismaz %d zīmi garai."
msgstr[2] "Šī parole ir pārāk īsa. Tai ir jābūt vismaz %d zīmēm garai."

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] "Parolei ir jābūt vismaz %(min_length)d zīmēm garai."
msgstr[1] "Parolei ir jābūt vismaz %(min_length)d zīmi garai."
msgstr[2] "Parolei ir jābūt vismaz %(min_length)d zīmēm garai."

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr "Parole ir pārāk līdzīga %(verbose_name)s."

msgid "Your password can’t be too similar to your other personal information."
msgstr "Parole nedrīkst būt pārāk līdzīga pārējai personīgajai informācijai."

msgid "This password is too common."
msgstr "Parole ir pārāk bieži lietota."

msgid "Your password can’t be a commonly used password."
msgstr "Parole nedrīkst būt no bieži lietotu paroļu saraksta."

msgid "This password is entirely numeric."
msgstr "Parole sastāv tikai no cipariem."

msgid "Your password can’t be entirely numeric."
msgstr "Parole nedrīkst sastāvēt tikai no cipariem."

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "Paroles pārstatīšana %(site_name)s"

msgid ""
"Enter a valid username. This value may contain only unaccented lowercase a-z "
"and uppercase A-Z letters, numbers, and @/./+/-/_ characters."
msgstr ""
"Ievadi derīgu lietotāja vārdu. Šī vērtība var saturēt tikai mazos a-z un "
"lielos A-Z burtus bez diakritiskajām zīmēm, ciparus un @/./+/-/_ simbolus."

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""
"Ievadi derīgu lietotāja vārdu. Šī vērtība var saturēt tikai burtus, ciparus "
"un @/./+/-/_ simbolus."

msgid "Logged out"
msgstr "Atslēdzies"

msgid "Password reset"
msgstr "Paroles atiestatīšana"

msgid "Password reset sent"
msgstr "Paroles atiestatīšanas informācija nosūtīta"

msgid "Enter new password"
msgstr "Ievadiet jauno paroli"

msgid "Password reset unsuccessful"
msgstr "Paroles atiestatīšana nesekmīga"

msgid "Password reset complete"
msgstr "Paroles atiestatīšana pabeigta"

msgid "Password change"
msgstr "Paroles maiņa"

msgid "Password change successful"
msgstr "Paroles maiņa sekmīga"
