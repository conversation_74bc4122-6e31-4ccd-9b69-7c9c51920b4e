# orgunit_app/models.py (formerly org_unit/models.py)
from django.db import models
from mptt.models import MPTTModel, TreeForeignKey

# Choices for Location Type
class LocationTypeChoices(models.TextChoices):
    HQ = 'HQ', 'Head Office'
    RGN = 'RGN', 'Region'
    CSC = 'CSC', 'Customer Service Center'
    BRH = 'BRH', 'Branch' # Added a common branch type

# Location model (MPTT for hierarchical locations)
class Location(MPTTModel):
    name = models.CharField(max_length=155, unique=True, help_text="Full name of the location (e.g., 'Addis Ababa Head Office')")
    abbreviation = models.CharField(max_length=20, null=True, blank=True, unique=True, help_text="Short code for the location (e.g., 'AAHO')")
    location_type = models.CharField(max_length=3, choices=LocationTypeChoices.choices, default=LocationTypeChoices.HQ, help_text="Type of location (e.g., Head Office, Region)")
    parent = models.ForeignKey(
        'self', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='sub_locations', 
        help_text="Parent location for hierarchical structure"
    )
    
    class MPTTMeta:
        order_insertion_by = ['name'] # Order by name for MPTT

    def __str__(self):
        return self.name

# Department model (MPTT for hierarchical departments)
class Department(MPTTModel):
    dept_name = models.CharField("Department Name", max_length=155, unique=True, help_text="Full name of the department (e.g., 'Human Resources')")
    short_name = models.CharField("Short Name", null=True, blank=True, max_length=50, help_text="Abbreviation or short name (e.g., 'HR')")
    parent = TreeForeignKey(
        'self', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        related_name='sub_departments',
        help_text="Parent department for hierarchical structure"
    )
    location = models.ForeignKey(
        Location, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='departments_at_location', 
        help_text="Primary location where this department is based"
    )
    
    class MPTTMeta:
        order_insertion_by = ['dept_name']
        
    def __str__(self):
        # Reverted to plain department name for default string representation
        return self.dept_name

    def get_tree_display_name(self):
        """
        Returns the department name with indentation for tree-like display.
        """
        # Ensure 'self.level' exists for MPTT objects
        # Use an appropriate indentation character, e.g., '---' or '·'
        return '--- ' * self.level + self.dept_name

    # Function to retrieve sub-departments
    def get_all_sub_departments(self):
        """
        Retrieves all immediate and nested sub-departments of the current department.
        Uses MPTT's get_descendants() method.
        """
        # get_descendants(include_self=False) gets all children, grandchildren, etc.
        # If you only want immediate children, use self.sub_departments.all() or self.get_children()
        return self.get_descendants(include_self=False)

    def get_immediate_sub_departments(self):
        """
        Retrieves only the immediate sub-departments (children) of the current department.
        """
        return self.get_children() # MPTT provides this method for direct children


# Position Model
class Position(models.Model):
    title = models.CharField(max_length=255, unique=True, help_text="Official title of the position (e.g., 'Software Engineer', 'HR Manager')")
    level = models.PositiveSmallIntegerField(
        default=1, # Default to 1 (e.g., Entry Level)
        help_text="The organizational level of this position (e.g., 1 for Junior, 7 for Executive). Lower numbers indicate lower levels."
    )
    description = models.TextField(blank=True, help_text="Detailed description of the position responsibilities")
    is_management = models.BooleanField(default=False, help_text="Indicates if this is a management position")
    
    class Meta:
        verbose_name = "Position"
        verbose_name_plural = "Positions"
        ordering = ['level', 'title'] # Order by level then title

    def __str__(self):
        return f"{self.title} (Level {self.level})"
