
{% extends 'base.html' %}

{% block content %}

<body class="p-8">
    <div class="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow-lg">
        {% if department %}
            <h1 class="text-3xl font-bold text-gray-800 mb-6 border-b pb-2">
                Department: <span class="text-indigo-600">{{ department.dept_name }}</span>
            </h1>

            <div class="mb-6">
                <h2 class="text-2xl font-semibold text-gray-700 mb-3">Details</h2>
                <p class="text-lg mb-2">
                    <strong>Short Name:</strong> {{ department.short_name|default:"N/A" }}
                </p>
                <p class="text-lg mb-2">
                    <strong>Location:</strong>
                    {% if department.location %}
                        {{ department.location.name }} ({{ department.location.address }})
                    {% else %}
                        N/A
                    {% endif %}
                </p>
                <p class="text-lg">
                    <strong>Parent Department:</strong>
                    {% if department.parent %}
                        {{ department.parent.dept_name }}
                    {% else %}
                        (Top Level Department)
                    {% endif %}
                </p>
            </div>

            <div class="mb-6">
                <h2 class="text-2xl font-semibold text-gray-700 mb-3">Immediate Sub-Departments</h2>
                {% if immediate_sub_departments %}
                    <ul class="list-disc pl-5 space-y-2">
                        {% for sub_dept in immediate_sub_departments %}
                            <li class="text-lg">
                                {# Changed the link to go to a PK-based detail view for other departments, assuming you still want that functionality. #}
                                {# If you want to only view the logged-in user's department, you might remove this link or link to a different view. #}
                                <a href="{% url 'departments:detail_with_pk' pk=sub_dept.pk %}"
                                   class="text-blue-600 hover:text-blue-800 hover:underline">
                                    {{ sub_dept.dept_name }} ({{ sub_dept.short_name|default:"N/A" }})
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <p class="text-lg text-gray-600">No immediate sub-departments found for this department.</p>
                {% endif %}
            </div>

            <div>
                <h2 class="text-2xl font-semibold text-gray-700 mb-3">All Descendant Sub-Departments (Nested)</h2>
                {% if all_sub_departments %}
                    <ul class="list-disc pl-5 space-y-2">
                        {% for sub_dept in all_sub_departments %}
                            <li class="text-lg">
                                {# Changed the link to go to a PK-based detail view for other departments, assuming you still want that functionality. #}
                                <a href="{% url 'departments:detail_with_pk' pk=sub_dept.pk %}"
                                   class="text-green-600 hover:text-green-800 hover:underline">
                                    {{ sub_dept.dept_name }} ({{ sub_dept.short_name|default:"N/A" }}) - Level: {{ sub_dept.level }}
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <p class="text-lg text-gray-600">No descendant sub-departments found for this department.</p>
                {% endif %}
            </div>
        {% else %}
            <h1 class="text-3xl font-bold text-gray-800 mb-6 border-b pb-2">
                No Department Found for Current User
            </h1>
            <p class="text-lg text-gray-600">
                Please ensure your user account is linked to an employee profile, and that employee profile has a department assigned.
            </p>
        {% endif %}

        <div class="mt-8 text-center">
            <a href="#" onclick="window.history.back()"
               class="inline-block bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-full shadow-md transition duration-300 ease-in-out">
                &larr; Back
            </a>
        </div>
    </div>
</body>

{% endblock %}