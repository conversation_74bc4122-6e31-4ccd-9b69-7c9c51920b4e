from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from .models import Department

#   department = get_object_or_404(Department, pk=pk)
#     sub_departments = department.get_all_sub_departments()
#     context = {
#         'department': department,
#         'sub_departments': sub_departments,
#     }
#     return render(request, 'orgunit_app/department_detail.html', context)

# Create your views here.
def department_detail_with_subdepts(request, pk):
    """
    Displays details of a specific department and its immediate sub-departments.
    """
    department = get_object_or_404(Department, pk=pk)
    
    # Get immediate sub-departments using the model method
    immediate_sub_departments = department.get_immediate_sub_departments()

    # Get all (immediate and nested) sub-departments if needed
    all_sub_departments = department.get_all_sub_departments()
    
    context = {
        'department': department,
        'immediate_sub_departments': immediate_sub_departments,
        'all_sub_departments': all_sub_departments,
    }
    return render(request, 'orgunit_app/department_detail.html', context)


@login_required # Ensures only logged-in users can access this view
def department_view_for_user(request):
    """
    Displays details of the logged-in user's department and its sub-departments.
    The user's department is determined via their associated Employee profile.
    """
    department = None
    # Check if the logged-in user has an associated Employee profile
    # and if that Employee profile has a department assigned.
    if hasattr(request.user, 'employee') and request.user.employee and request.user.employee.department:
        department = request.user.employee.department
    else:
        # Fallback if user has no associated Employee profile or no department assigned to employee
        # You might want to redirect to a different page or show an error
        pass # The template will display a "No department found" message

    immediate_sub_departments = []
    all_sub_departments = []
    if department:
        immediate_sub_departments = department.get_immediate_sub_departments()
        all_sub_departments = department.get_all_sub_departments()
    
    context = {
        'department': department,
        'immediate_sub_departments': immediate_sub_departments,
        'all_sub_departments': all_sub_departments,
    }
    return render(request, 'orgunit_app/my_department_detail.html', context)

