<!DOCTYPE html>
<html lang="en">
<head>
    {% load static %}
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}EEU Attendance and Leave Management System{% endblock %}</title>
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="{% static 'dist/output.css' %}">
    <!-- FontAwesome -->
    <link rel="stylesheet" href="{% static 'fontawesome/css/all.min.css' %}">

    <!-- Custom CSS for Navigation -->
    <style>
        .nav-menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0.5rem;
            color: #6b7280; /* text-gray-500 */
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            text-decoration: none;
        }

        .nav-menu-item:hover {
            background-color: #334155; /* bg-slate-700 */
            color: #f1f5f9; /* text-slate-100 */
        }

        .nav-menu-item.active {
            border-left: 4px solid #10b981; /* border-emerald-500 */
            background-color: #334155; /* bg-slate-700 */
            color: #34d399; /* text-emerald-400 */
        }

        .nav-menu-item.logout:hover {
            background-color: #ef4444; /* bg-red-500 */
            color: white;
        }

        .nav-menu-item i {
            font-size: 1.25rem; /* text-xl */
            margin-bottom: 0.25rem; /* mb-1 */
        }

        .nav-menu-item span {
            font-size: 0.75rem; /* text-xs */
            font-weight: 500; /* font-medium */
        }

        /* Dropdown Menu Styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            background-color: white;
            min-width: 200px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            border-radius: 0.5rem;
            z-index: 1000;
            border: 1px solid #e5e7eb;
        }

        .dropdown-content a {
            color: #374151;
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: background-color 0.2s;
        }

        .dropdown-content a:hover {
            background-color: #f3f4f6;
        }

        .dropdown-content a:first-child {
            border-radius: 0.5rem 0.5rem 0 0;
        }

        .dropdown-content a:last-child {
            border-radius: 0 0 0.5rem 0.5rem;
        }

        .dropdown-content a i {
            margin-right: 8px;
            width: 16px;
            color: #10b981;
        }

        .dropdown.show .dropdown-content {
            display: block;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50 font-sans">
    <div class="flex h-screen">
        <!-- Sidebar Navigation -->
        <nav class="bg-slate-800 text-gray-500 w-20 flex flex-col items-center py-2 shadow-lg">
            <!-- Logo/Brand -->
            <div class="mb-8">
                <div class="flex items-center justify-center">
                    <img src="{% static 'imgs/eeu_logo_w.png' %}" alt="EEU Logo" class="w-12 h-12 object-contain">
                </div>
            </div>

            <!-- Navigation Items -->
            <div class="flex flex-col space-y-4">
                <!-- Dashboard -->
                <a href="{% url 'dashboard:dashboard' %}" class="nav-menu-item {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>

                <!-- Attendance -->
                <a href="{% url 'attendance_app:index' %}" class="nav-menu-item {% if request.resolver_match.namespace == 'attendance_app' %}active{% endif %}">
                    <i class="fas fa-clock"></i>
                    <span>Attendance</span>
                </a>

                <!-- Leave Management -->
                <a href="{% url 'leave_app:index' %}" class="nav-menu-item {% if request.resolver_match.namespace == 'leave_app' %}active{% endif %}">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Leave</span>
                </a>

                <!-- Employees -->
                <a href="#" class="nav-menu-item">
                    <i class="fas fa-users"></i>
                    <span>Employees</span>
                </a>

                <!-- Reports -->
                <a href="{% url 'report_app:index' %}" class="nav-menu-item {% if request.resolver_match.namespace == 'report_app' %}active{% endif %}">
                    <i class="fas fa-chart-bar"></i>
                    <span>Reports</span>
                </a>
            </div>

            <!-- User Menu at Bottom -->
            <div class="mt-auto">
                {% if user.is_authenticated %}
                    <div class="flex flex-col items-center space-y-4">
                        <!-- Settings -->
                        <a href="#" class="nav-menu-item">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>

                        <!-- Help -->
                        <a href="#" class="nav-menu-item">
                            <i class="fas fa-question-circle"></i>
                            <span>Help</span>
                        </a>
                    </div>
                {% else %}
                    <a href="#" class="nav-menu-item">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>Login</span>
                    </a>
                {% endif %}
            </div>
        </nav>

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-1">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">{% block page_title %}Dashboard{% endblock %}</h1>
                        <p class="text-xs text-gray-600">{% block page_subtitle %}Welcome to EEU Attendance and Leave Management System{% endblock %}</p>
                    </div>

                    {% if user.is_authenticated %}
                        <div class="flex items-center space-x-4">
                            <!-- Notifications -->
                            <button class="relative p-2 text-gray-400 hover:text-emerald-500 transition-colors duration-200">
                                <i class="fas fa-bell text-lg"></i>
                                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                            </button>

                            <!-- User Dropdown -->
                            <div class="dropdown">
                                <button onclick="toggleDropdown()" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                    <div class="text-right">
                                        <p class="text-sm font-medium text-gray-900">{{ user.get_full_name|default:user.username }}</p>
                                        <p class="text-xs text-gray-500">
                                            {% if user.employee_profile %}
                                                {{ user.employee_profile.position.name|default:"Employee" }}
                                            {% else %}
                                                User
                                            {% endif %}
                                        </p>
                                    </div>
                                    <div class="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-white text-sm"></i>
                                    </div>
                                    <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                                </button>

                                <div class="dropdown-content">
                                    <a href="#" class="dropdown-item">
                                        <i class="fas fa-user"></i>
                                        Profile
                                    </a>
                                    <a href="{% url 'accounts:password_change' %}" class="dropdown-item">
                                        <i class="fas fa-key"></i>
                                        Change Password
                                    </a>
                                    <a href="{% url 'accounts:logout' %}" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt"></i>
                                        Logout
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto bg-gray-50 p-6">
                {% block content %}
                {% endblock %}
            </main>
        </div>
    </div>

    {% block extra_js %}{% endblock %}

    <!-- Dropdown JavaScript -->
    <script>
        function toggleDropdown() {
            document.querySelector('.dropdown').classList.toggle('show');
        }

        // Close dropdown when clicking outside
        window.onclick = function(event) {
            if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button')) {
                var dropdowns = document.getElementsByClassName("dropdown");
                for (var i = 0; i < dropdowns.length; i++) {
                    var openDropdown = dropdowns[i];
                    if (openDropdown.classList.contains('show')) {
                        openDropdown.classList.remove('show');
                    }
                }
            }
        }
    </script>
</body>
</html>

