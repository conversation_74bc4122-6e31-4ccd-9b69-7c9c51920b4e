{% extends 'base.html' %}

{% block title %}Leave Management - EEU System{% endblock %}

{% block page_title %}Leave Management{% endblock %}
{% block page_subtitle %}Manage leave requests, approvals, and leave balances{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-xl font-semibold text-gray-900 mb-2">
                    <i class="fas fa-calendar-alt text-emerald-500 mr-2"></i>
                    Leave Management System
                </h2>
                <p class="text-gray-600">
                    Submit leave requests, track approvals, and manage your leave balance.
                </p>
            </div>
            <div class="text-right">
                <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-calendar-alt text-2xl text-emerald-600"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Leave Balance Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Annual Leave -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calendar-check text-xl text-emerald-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Annual Leave</p>
                    <p class="text-2xl font-bold text-gray-900">15 <span class="text-sm font-normal text-gray-500">days</span></p>
                    <p class="text-xs text-gray-500">Available</p>
                </div>
            </div>
        </div>

        <!-- Sick Leave -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-md text-xl text-red-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Sick Leave</p>
                    <p class="text-2xl font-bold text-gray-900">10 <span class="text-sm font-normal text-gray-500">days</span></p>
                    <p class="text-xs text-gray-500">Available</p>
                </div>
            </div>
        </div>

        <!-- Personal Leave -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-clock text-xl text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Personal Leave</p>
                    <p class="text-2xl font-bold text-gray-900">5 <span class="text-sm font-normal text-gray-500">days</span></p>
                    <p class="text-xs text-gray-500">Available</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Request Leave -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div class="text-center">
                <div class="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-calendar-plus text-xl text-emerald-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Request Leave</h3>
                <p class="text-sm text-gray-600 mb-4">Submit a new leave request</p>
                <button class="w-full bg-emerald-500 text-white py-2 px-4 rounded-lg hover:bg-emerald-600 transition-colors">
                    New Request
                </button>
            </div>
        </div>

        <!-- My Requests -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div class="text-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-list-alt text-xl text-blue-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">My Requests</h3>
                <p class="text-sm text-gray-600 mb-4">View submitted requests</p>
                <button class="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors">
                    View Requests
                </button>
            </div>
        </div>

        <!-- Leave Calendar -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div class="text-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-calendar text-xl text-purple-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Leave Calendar</h3>
                <p class="text-sm text-gray-600 mb-4">View team leave schedule</p>
                <button class="w-full bg-purple-500 text-white py-2 px-4 rounded-lg hover:bg-purple-600 transition-colors">
                    View Calendar
                </button>
            </div>
        </div>

        <!-- Leave History -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div class="text-center">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-history text-xl text-orange-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Leave History</h3>
                <p class="text-sm text-gray-600 mb-4">View past leave records</p>
                <button class="w-full bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors">
                    View History
                </button>
            </div>
        </div>
    </div>

    <!-- Recent Leave Requests -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Recent Leave Requests</h3>
            <p class="text-sm text-gray-600">Latest leave requests and their status</p>
        </div>
        <div class="p-6">
            <div class="text-center py-8">
                <i class="fas fa-calendar-alt text-4xl text-gray-300 mb-4"></i>
                <p class="text-gray-500">No recent leave requests</p>
                <p class="text-sm text-gray-400 mt-2">Leave management features will be implemented here</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
