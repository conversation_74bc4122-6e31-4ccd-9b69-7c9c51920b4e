{% extends 'base.html' %}

{% block title %}Login - EEU System{% endblock %}

{% block page_title %}Login{% endblock %}
{% block page_subtitle %}Sign in to your account{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-emerald-100">
                <i class="fas fa-user text-emerald-600 text-xl"></i>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Sign in to your account
            </h2>
        </div>
        <form class="mt-8 space-y-6" method="post">
            {% csrf_token %}
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="{{ form.username.id_for_label }}" class="sr-only">Username</label>
                    {{ form.username }}
                </div>
                <div>
                    <label for="{{ form.password.id_for_label }}" class="sr-only">Password</label>
                    {{ form.password }}
                </div>
            </div>

            {% if form.errors %}
                <div class="text-red-600 text-sm">
                    {{ form.errors }}
                </div>
            {% endif %}

            <div>
                <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fas fa-lock text-emerald-500 group-hover:text-emerald-400"></i>
                    </span>
                    Sign in
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
