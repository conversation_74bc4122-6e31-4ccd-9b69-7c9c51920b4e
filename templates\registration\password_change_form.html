{% extends 'base.html' %}

{% block title %}Change Password - EEU System{% endblock %}

{% block page_title %}Change Password{% endblock %}
{% block page_subtitle %}Update your account password{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-2">
                <i class="fas fa-key text-emerald-500 mr-2"></i>
                Change Your Password
            </h2>
            <p class="text-gray-600">
                Please enter your current password and choose a new password.
            </p>
        </div>

        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div>
                <label for="{{ form.old_password.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Current Password
                </label>
                {{ form.old_password }}
                {% if form.old_password.errors %}
                    <div class="text-red-600 text-sm mt-1">
                        {{ form.old_password.errors }}
                    </div>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.new_password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    New Password
                </label>
                {{ form.new_password1 }}
                {% if form.new_password1.errors %}
                    <div class="text-red-600 text-sm mt-1">
                        {{ form.new_password1.errors }}
                    </div>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.new_password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Confirm New Password
                </label>
                {{ form.new_password2 }}
                {% if form.new_password2.errors %}
                    <div class="text-red-600 text-sm mt-1">
                        {{ form.new_password2.errors }}
                    </div>
                {% endif %}
            </div>

            <div class="flex items-center justify-between pt-4">
                <a href="{% url 'dashboard:dashboard' %}" class="text-emerald-600 hover:text-emerald-500 text-sm font-medium">
                    <i class="fas fa-arrow-left mr-1"></i>
                    Back to Dashboard
                </a>
                <button type="submit" class="bg-emerald-600 text-white py-2 px-6 rounded-lg hover:bg-emerald-700 transition-colors font-medium">
                    <i class="fas fa-save mr-2"></i>
                    Change Password
                </button>
            </div>
        </form>
    </div>
</div>

<style>
    input[type="password"] {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        transition: border-color 0.2s;
    }
    
    input[type="password"]:focus {
        outline: none;
        border-color: #10b981;
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }
</style>
{% endblock %}
